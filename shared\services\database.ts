/**
 * UNIFIED DATABASE SERVICE
 * Provides consistent database operations for both admin and staff servers
 * Fixes the dual database architecture issues
 */

import { Pool, PoolClient } from 'pg';
import { UserRole } from '../types/common';

// Database configuration
const dbConfig = {
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { 
    rejectUnauthorized: process.env.DB_REJECT_UNAUTHORIZED !== 'false' 
  } : false,
  max: parseInt(process.env.DB_POOL_MAX || '20'),
  min: parseInt(process.env.DB_POOL_MIN || '2'),
  idleTimeoutMillis: parseInt(process.env.DB_POOL_IDLE_TIMEOUT || '30000'),
  connectionTimeoutMillis: 2000,
};

// Validate configuration
if (!dbConfig.connectionString) {
  throw new Error('DATABASE_URL environment variable is required');
}

// Create connection pool
const pool = new Pool(dbConfig);

// Handle pool errors
pool.on('error', (err) => {
  console.error('Unexpected error on idle client', err);
  process.exit(-1);
});

// User interface for database operations
export interface DatabaseUser {
  id: string;
  email: string;
  password_hash: string;
  role: UserRole;
  name: string;
  is_active: boolean;
  server_type: 'admin' | 'staff';
  last_login?: Date;
  failed_login_attempts: number;
  locked_until?: Date;
  created_at: Date;
  updated_at: Date;
}

/**
 * Database service class with unified operations
 */
export class DatabaseService {
  private pool: Pool;

  constructor() {
    this.pool = pool;
  }

  /**
   * Get a client from the pool
   */
  async getClient(): Promise<PoolClient> {
    return this.pool.connect();
  }

  /**
   * Execute a query with automatic client management
   */
  async query<T = any>(text: string, params?: any[]): Promise<T[]> {
    const client = await this.getClient();
    try {
      const result = await client.query(text, params);
      return result.rows;
    } finally {
      client.release();
    }
  }

  /**
   * Execute a transaction
   */
  async transaction<T>(callback: (client: PoolClient) => Promise<T>): Promise<T> {
    const client = await this.getClient();
    try {
      await client.query('BEGIN');
      const result = await callback(client);
      await client.query('COMMIT');
      return result;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  // =============================================================================
  // USER MANAGEMENT OPERATIONS
  // =============================================================================

  /**
   * Find user by email (unified across both servers)
   */
  async findUserByEmail(email: string): Promise<DatabaseUser | null> {
    const users = await this.query<DatabaseUser>(
      'SELECT * FROM users WHERE email = $1 AND is_active = true',
      [email]
    );
    return users[0] || null;
  }

  /**
   * Find user by ID
   */
  async findUserById(id: string): Promise<DatabaseUser | null> {
    const users = await this.query<DatabaseUser>(
      'SELECT * FROM users WHERE id = $1',
      [id]
    );
    return users[0] || null;
  }

  /**
   * Create new user with automatic server_type assignment
   */
  async createUser(userData: {
    email: string;
    password_hash: string;
    role: UserRole;
    name: string;
  }): Promise<DatabaseUser> {
    const serverType = this.getServerTypeForRole(userData.role);
    
    const users = await this.query<DatabaseUser>(
      `INSERT INTO users (email, password_hash, role, name, server_type, is_active, created_at, updated_at)
       VALUES ($1, $2, $3, $4, $5, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
       RETURNING *`,
      [userData.email, userData.password_hash, userData.role, userData.name, serverType]
    );
    
    return users[0];
  }

  /**
   * Update user login tracking
   */
  async updateUserLogin(userId: string, success: boolean = true): Promise<void> {
    if (success) {
      await this.query(
        `UPDATE users 
         SET last_login = CURRENT_TIMESTAMP, 
             failed_login_attempts = 0, 
             locked_until = NULL,
             updated_at = CURRENT_TIMESTAMP
         WHERE id = $1`,
        [userId]
      );
    } else {
      await this.query(
        `UPDATE users 
         SET failed_login_attempts = COALESCE(failed_login_attempts, 0) + 1,
             locked_until = CASE 
               WHEN COALESCE(failed_login_attempts, 0) + 1 >= 5 
               THEN CURRENT_TIMESTAMP + INTERVAL '30 minutes'
               ELSE locked_until
             END,
             updated_at = CURRENT_TIMESTAMP
         WHERE id = $1`,
        [userId]
      );
    }
  }

  /**
   * Get users by server type
   */
  async getUsersByServerType(serverType: 'admin' | 'staff'): Promise<DatabaseUser[]> {
    return this.query<DatabaseUser>(
      'SELECT * FROM users WHERE server_type = $1 AND is_active = true ORDER BY name',
      [serverType]
    );
  }

  /**
   * Get users by role
   */
  async getUsersByRole(role: UserRole): Promise<DatabaseUser[]> {
    return this.query<DatabaseUser>(
      'SELECT * FROM users WHERE role = $1 AND is_active = true ORDER BY name',
      [role]
    );
  }

  /**
   * Update user
   */
  async updateUser(id: string, updates: Partial<DatabaseUser>): Promise<DatabaseUser | null> {
    const setClause = Object.keys(updates)
      .map((key, index) => `${key} = $${index + 2}`)
      .join(', ');
    
    const values = [id, ...Object.values(updates)];
    
    const users = await this.query<DatabaseUser>(
      `UPDATE users SET ${setClause}, updated_at = CURRENT_TIMESTAMP WHERE id = $1 RETURNING *`,
      values
    );
    
    return users[0] || null;
  }

  /**
   * Deactivate user (soft delete)
   */
  async deactivateUser(id: string): Promise<boolean> {
    const result = await this.query(
      'UPDATE users SET is_active = false, updated_at = CURRENT_TIMESTAMP WHERE id = $1',
      [id]
    );
    return result.length > 0;
  }

  // =============================================================================
  // UTILITY METHODS
  // =============================================================================

  /**
   * Determine server type based on user role
   */
  private getServerTypeForRole(role: UserRole): 'admin' | 'staff' {
    const adminRoles: UserRole[] = [UserRole.ADMIN, UserRole.CASHIER, UserRole.ACCOUNTANT];
    return adminRoles.includes(role) ? 'admin' : 'staff';
  }

  /**
   * Check if user account is locked
   */
  async isUserLocked(userId: string): Promise<boolean> {
    const users = await this.query<{ locked_until: Date | null }>(
      'SELECT locked_until FROM users WHERE id = $1',
      [userId]
    );
    
    if (!users[0] || !users[0].locked_until) {
      return false;
    }
    
    return new Date() < new Date(users[0].locked_until);
  }

  /**
   * Get database health status
   */
  async getHealthStatus(): Promise<{ healthy: boolean; message: string }> {
    try {
      await this.query('SELECT 1');
      return { healthy: true, message: 'Database connection is healthy' };
    } catch (error) {
      return { 
        healthy: false, 
        message: `Database connection failed: ${error instanceof Error ? error.message : 'Unknown error'}` 
      };
    }
  }

  /**
   * Close all connections
   */
  async close(): Promise<void> {
    await this.pool.end();
  }
}

// Export singleton instance
export const db = new DatabaseService();

// Export pool for direct access if needed
export { pool };

// Export types
export type { DatabaseUser };
