/**
 * UNIFIED AUTHENTICATION MIDDLEWARE
 * Provides consistent authentication across admin and staff servers
 * Fixes authentication flow inconsistencies and security gaps
 */

import { NextRequest, NextResponse } from 'next/server';
import { 
  validateTokenAndGetUser, 
  extractToken<PERSON>rom<PERSON>eader, 
  hasR<PERSON>, 
  isAd<PERSON><PERSON><PERSON>, 
  isSta<PERSON><PERSON><PERSON>,
  AuthR<PERSON>ult 
} from '../utils/auth';
import { createUnauthorizedResponse, createForbiddenResponse } from '../utils/api-response';
import { UserRole } from '../types/common';
import { db } from '../services/database';

export interface AuthContext {
  user: {
    id: string;
    email: string;
    role: UserRole;
    name: string;
    serverType: 'admin' | 'staff';
  };
  token: string;
}

export interface AuthMiddlewareOptions {
  requiredRoles?: UserRole[];
  serverType?: 'admin' | 'staff';
  allowCrossServer?: boolean;
  skipInactiveCheck?: boolean;
}

/**
 * Main authentication middleware function
 */
export async function authenticateRequest(
  request: NextRequest,
  options: AuthMiddlewareOptions = {}
): Promise<{ success: true; context: AuthContext } | { success: false; response: NextResponse }> {
  
  // Extract token from Authorization header
  const authHeader = request.headers.get('authorization');
  const token = extractTokenFromHeader(authHeader);
  
  if (!token) {
    return {
      success: false,
      response: createUnauthorizedResponse('Authorization token required')
    };
  }

  // Validate token and get user info
  const authResult = validateTokenAndGetUser(token);
  
  if (!authResult.success || !authResult.user) {
    return {
      success: false,
      response: createUnauthorizedResponse(authResult.error || 'Invalid token')
    };
  }

  // Get full user details from database
  const dbUser = await db.findUserById(authResult.user.id);
  
  if (!dbUser) {
    return {
      success: false,
      response: createUnauthorizedResponse('User not found')
    };
  }

  // Check if user is active (unless skipped)
  if (!options.skipInactiveCheck && !dbUser.is_active) {
    return {
      success: false,
      response: createUnauthorizedResponse('Account is deactivated')
    };
  }

  // Check if account is locked
  const isLocked = await db.isUserLocked(dbUser.id);
  if (isLocked) {
    return {
      success: false,
      response: createUnauthorizedResponse('Account is temporarily locked')
    };
  }

  // Check server type restrictions
  if (options.serverType && !options.allowCrossServer) {
    const userServerType = isAdminUser(dbUser.role) ? 'admin' : 'staff';
    if (userServerType !== options.serverType) {
      return {
        success: false,
        response: createForbiddenResponse(`Access restricted to ${options.serverType} server users`)
      };
    }
  }

  // Check role requirements
  if (options.requiredRoles && options.requiredRoles.length > 0) {
    if (!hasRole(dbUser.role, options.requiredRoles)) {
      return {
        success: false,
        response: createForbiddenResponse('Insufficient permissions')
      };
    }
  }

  // Create auth context
  const context: AuthContext = {
    user: {
      id: dbUser.id,
      email: dbUser.email,
      role: dbUser.role,
      name: dbUser.name,
      serverType: dbUser.server_type
    },
    token
  };

  return { success: true, context };
}

/**
 * Higher-order function to wrap API routes with authentication
 */
export function withAuth<T extends any[]>(
  handler: (request: NextRequest, context: AuthContext, ...args: T) => Promise<NextResponse>,
  options: AuthMiddlewareOptions = {}
) {
  return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
    const authResult = await authenticateRequest(request, options);
    
    if (!authResult.success) {
      return authResult.response;
    }
    
    return handler(request, authResult.context, ...args);
  };
}

/**
 * Middleware for admin-only routes
 */
export function withAdminAuth<T extends any[]>(
  handler: (request: NextRequest, context: AuthContext, ...args: T) => Promise<NextResponse>
) {
  return withAuth(handler, {
    requiredRoles: [UserRole.ADMIN],
    serverType: 'admin'
  });
}

/**
 * Middleware for admin server routes (admin, cashier, accountant)
 */
export function withAdminServerAuth<T extends any[]>(
  handler: (request: NextRequest, context: AuthContext, ...args: T) => Promise<NextResponse>
) {
  return withAuth(handler, {
    requiredRoles: [UserRole.ADMIN, UserRole.CASHIER, UserRole.ACCOUNTANT],
    serverType: 'admin'
  });
}

/**
 * Middleware for staff server routes (management, reception, teacher)
 */
export function withStaffServerAuth<T extends any[]>(
  handler: (request: NextRequest, context: AuthContext, ...args: T) => Promise<NextResponse>
) {
  return withAuth(handler, {
    requiredRoles: [UserRole.MANAGEMENT, UserRole.RECEPTION, UserRole.TEACHER],
    serverType: 'staff'
  });
}

/**
 * Middleware for management-only routes
 */
export function withManagementAuth<T extends any[]>(
  handler: (request: NextRequest, context: AuthContext, ...args: T) => Promise<NextResponse>
) {
  return withAuth(handler, {
    requiredRoles: [UserRole.MANAGEMENT],
    serverType: 'staff'
  });
}

/**
 * Middleware for cross-server routes (allows both admin and staff users)
 */
export function withCrossServerAuth<T extends any[]>(
  handler: (request: NextRequest, context: AuthContext, ...args: T) => Promise<NextResponse>,
  requiredRoles?: UserRole[]
) {
  return withAuth(handler, {
    requiredRoles,
    allowCrossServer: true
  });
}

/**
 * Middleware for financial operations (admin, cashier, accountant)
 */
export function withFinancialAuth<T extends any[]>(
  handler: (request: NextRequest, context: AuthContext, ...args: T) => Promise<NextResponse>
) {
  return withAuth(handler, {
    requiredRoles: [UserRole.ADMIN, UserRole.CASHIER, UserRole.ACCOUNTANT],
    serverType: 'admin'
  });
}

/**
 * Extract user context from authenticated request
 * Use this in middleware that has already been authenticated
 */
export async function getUserContext(request: NextRequest): Promise<AuthContext | null> {
  const authResult = await authenticateRequest(request);
  return authResult.success ? authResult.context : null;
}

/**
 * Check if current user has specific permission
 */
export function hasPermission(context: AuthContext, permission: string): boolean {
  // Define permission mappings
  const permissions: Record<UserRole, string[]> = {
    [UserRole.ADMIN]: ['*'], // Admin has all permissions
    [UserRole.CASHIER]: ['payments', 'students', 'reports:financial'],
    [UserRole.ACCOUNTANT]: ['payments', 'reports:financial', 'reports:accounting'],
    [UserRole.MANAGEMENT]: ['students', 'teachers', 'classes', 'reports:academic'],
    [UserRole.RECEPTION]: ['students', 'classes:view'],
    [UserRole.TEACHER]: ['classes:own', 'students:own']
  };

  const userPermissions = permissions[context.user.role] || [];
  
  // Admin has all permissions
  if (userPermissions.includes('*')) {
    return true;
  }
  
  // Check specific permission
  return userPermissions.includes(permission);
}

/**
 * Rate limiting helper for authentication endpoints
 */
export class AuthRateLimiter {
  private attempts: Map<string, { count: number; resetTime: number }> = new Map();
  private maxAttempts: number;
  private windowMs: number;

  constructor(maxAttempts: number = 5, windowMs: number = 15 * 60 * 1000) {
    this.maxAttempts = maxAttempts;
    this.windowMs = windowMs;
  }

  isRateLimited(identifier: string): boolean {
    const now = Date.now();
    const record = this.attempts.get(identifier);

    if (!record || now > record.resetTime) {
      this.attempts.set(identifier, { count: 1, resetTime: now + this.windowMs });
      return false;
    }

    if (record.count >= this.maxAttempts) {
      return true;
    }

    record.count++;
    return false;
  }

  reset(identifier: string): void {
    this.attempts.delete(identifier);
  }
}

// Export singleton rate limiter for login attempts
export const loginRateLimiter = new AuthRateLimiter(5, 15 * 60 * 1000); // 5 attempts per 15 minutes
