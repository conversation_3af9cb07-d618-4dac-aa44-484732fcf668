# PRODUCTION DOCKER COMPOSE CONFIGURATION
# Optimized for production deployment with security and performance

version: '3.8'

services:
  # Admin Server
  admin-server:
    build:
      context: .
      dockerfile: admin-server/Dockerfile.production
    container_name: innovative-admin-server
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - JWT_SECRET=${JWT_SECRET}
      - ADMIN_SERVER_URL=https://admin.innovativecentre.com
      - STAFF_SERVER_URL=https://staff.innovativecentre.com
      - INTER_SERVER_API_KEY=${INTER_SERVER_API_KEY}
      - SERVER_TYPE=admin
    volumes:
      - admin-logs:/app/logs
      - admin-uploads:/app/uploads
    networks:
      - innovative-network
    depends_on:
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/internal/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # Staff Server
  staff-server:
    build:
      context: .
      dockerfile: staff-server/Dockerfile.production
    container_name: innovative-staff-server
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - JWT_SECRET=${JWT_SECRET}
      - ADMIN_SERVER_URL=https://admin.innovativecentre.com
      - STAFF_SERVER_URL=https://staff.innovativecentre.com
      - INTER_SERVER_API_KEY=${INTER_SERVER_API_KEY}
      - SERVER_TYPE=staff
    volumes:
      - staff-logs:/app/logs
      - staff-uploads:/app/uploads
    networks:
      - innovative-network
    depends_on:
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/internal/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: innovative-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - innovative-network
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    container_name: innovative-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx-logs:/var/log/nginx
    networks:
      - innovative-network
    depends_on:
      - admin-server
      - staff-server
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: innovative-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    networks:
      - innovative-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  # Grafana for monitoring dashboards (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: innovative-grafana
    restart: unless-stopped
    ports:
      - "3003:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana-data:/var/lib/grafana
    networks:
      - innovative-network
    depends_on:
      - prometheus

networks:
  innovative-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  admin-logs:
    driver: local
  staff-logs:
    driver: local
  admin-uploads:
    driver: local
  staff-uploads:
    driver: local
  redis-data:
    driver: local
  nginx-logs:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
