/**
 * UNIFIED AUTHENTICATION UTILITIES
 * Fixes JWT secret inconsistencies and authentication flow issues
 */

import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { UserRole } from '../types/common';

// Unified JWT configuration
const JWT_CONFIG = {
  // Use unified JWT_SECRET with fallbacks for backward compatibility
  secret: process.env.JWT_SECRET || process.env.ADMIN_JWT_SECRET || process.env.STAFF_JWT_SECRET,
  expiresIn: process.env.JWT_EXPIRES_IN || '24h',
  refreshExpiresIn: process.env.REFRESH_TOKEN_EXPIRES_IN || '7d'
};

// Validate JWT configuration
if (!JWT_CONFIG.secret) {
  throw new Error('JWT_SECRET environment variable is required');
}

export interface TokenPayload {
  userId: string;
  email: string;
  role: UserRole;
  serverType?: 'admin' | 'staff';
  iat?: number;
  exp?: number;
}

export interface AuthResult {
  success: boolean;
  user?: {
    id: string;
    email: string;
    role: UserRole;
    name: string;
    serverType?: 'admin' | 'staff';
  };
  token?: string;
  refreshToken?: string;
  error?: string;
}

/**
 * Generate JWT token with unified secret
 */
export function generateToken(payload: Omit<TokenPayload, 'iat' | 'exp'>): string {
  return jwt.sign(payload, JWT_CONFIG.secret, {
    expiresIn: JWT_CONFIG.expiresIn
  });
}

/**
 * Generate refresh token
 */
export function generateRefreshToken(payload: Omit<TokenPayload, 'iat' | 'exp'>): string {
  return jwt.sign(payload, JWT_CONFIG.secret, {
    expiresIn: JWT_CONFIG.refreshExpiresIn
  });
}

/**
 * Verify JWT token with unified secret
 */
export function verifyToken(token: string): TokenPayload | null {
  try {
    const decoded = jwt.verify(token, JWT_CONFIG.secret) as TokenPayload;
    return decoded;
  } catch (error) {
    console.error('Token verification failed:', error);
    return null;
  }
}

/**
 * Hash password with consistent rounds
 */
export async function hashPassword(password: string): Promise<string> {
  const rounds = parseInt(process.env.BCRYPT_ROUNDS || '12');
  return bcrypt.hash(password, rounds);
}

/**
 * Verify password against hash
 */
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash);
}

/**
 * Extract token from Authorization header
 */
export function extractTokenFromHeader(authHeader: string | null): string | null {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

/**
 * Check if user has required role
 */
export function hasRole(userRole: UserRole, requiredRoles: UserRole[]): boolean {
  return requiredRoles.includes(userRole);
}

/**
 * Check if user is admin server user
 */
export function isAdminUser(role: UserRole): boolean {
  return [UserRole.ADMIN, UserRole.CASHIER, UserRole.ACCOUNTANT].includes(role);
}

/**
 * Check if user is staff server user
 */
export function isStaffUser(role: UserRole): boolean {
  return [UserRole.MANAGEMENT, UserRole.RECEPTION, UserRole.TEACHER].includes(role);
}

/**
 * Get server type based on user role
 */
export function getServerType(role: UserRole): 'admin' | 'staff' {
  return isAdminUser(role) ? 'admin' : 'staff';
}

/**
 * Validate token and return user info
 * This replaces the inconsistent validation logic across servers
 */
export function validateTokenAndGetUser(token: string): AuthResult {
  try {
    const payload = verifyToken(token);
    
    if (!payload) {
      return {
        success: false,
        error: 'Invalid or expired token'
      };
    }

    return {
      success: true,
      user: {
        id: payload.userId,
        email: payload.email,
        role: payload.role,
        name: '', // Will be populated from database
        serverType: payload.serverType || getServerType(payload.role)
      }
    };
  } catch (error) {
    return {
      success: false,
      error: 'Token validation failed'
    };
  }
}

/**
 * Create authentication tokens for user
 */
export function createAuthTokens(user: {
  id: string;
  email: string;
  role: UserRole;
}): { token: string; refreshToken: string } {
  const payload: Omit<TokenPayload, 'iat' | 'exp'> = {
    userId: user.id,
    email: user.email,
    role: user.role,
    serverType: getServerType(user.role)
  };

  return {
    token: generateToken(payload),
    refreshToken: generateRefreshToken(payload)
  };
}

/**
 * Refresh access token using refresh token
 */
export function refreshAccessToken(refreshToken: string): AuthResult {
  try {
    const payload = verifyToken(refreshToken);
    
    if (!payload) {
      return {
        success: false,
        error: 'Invalid refresh token'
      };
    }

    // Generate new access token
    const newToken = generateToken({
      userId: payload.userId,
      email: payload.email,
      role: payload.role,
      serverType: payload.serverType
    });

    return {
      success: true,
      token: newToken,
      user: {
        id: payload.userId,
        email: payload.email,
        role: payload.role,
        name: '',
        serverType: payload.serverType
      }
    };
  } catch (error) {
    return {
      success: false,
      error: 'Token refresh failed'
    };
  }
}

/**
 * Generate API key for inter-server communication
 */
export function generateApiKey(): string {
  return jwt.sign(
    { 
      type: 'api_key', 
      generated: Date.now() 
    }, 
    JWT_CONFIG.secret,
    { expiresIn: '1y' }
  );
}

/**
 * Verify API key for inter-server communication
 */
export function verifyApiKey(apiKey: string): boolean {
  try {
    const decoded = jwt.verify(apiKey, JWT_CONFIG.secret) as any;
    return decoded.type === 'api_key';
  } catch (error) {
    return false;
  }
}
