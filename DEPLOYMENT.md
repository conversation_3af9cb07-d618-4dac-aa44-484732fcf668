# 🚀 Deployment Guide - Innovative Centre Platform

## Overview

This guide covers the deployment of the Innovative Centre Platform, which consists of two Next.js servers (Admin and Staff) with a unified PostgreSQL database and shared services architecture.

## 🏗️ Architecture

- **Admin Server** (Port 3000): Handles admin, cashier, and accountant users
- **Staff Server** (Port 3001): Handles management, reception, and teacher users
- **Shared Database**: Single PostgreSQL database for all user and application data
- **Inter-Server Communication**: Secure API-based communication between servers
- **Monitoring**: Health checks and performance monitoring

## 📋 Prerequisites

### System Requirements
- Node.js 18+ and npm
- PostgreSQL 14+ (or Neon database)
- SSL certificates (for production)
- Domain names (for production)

### Database Setup
1. **Admin Database**: Financial records, invoices, payments, cabinets
2. **Staff Database**: Students, leads, groups, activity logs

## 🚀 Deployment Steps

### 1. Admin Server Deployment

#### Environment Configuration
Create `.env.local` in `admin-server/`:
```env
# Database
DATABASE_URL=postgresql://admin_owner:password@host:port/admin?sslmode=require

# Authentication
JWT_SECRET=secure-admin-jwt-secret-production
NEXTAUTH_SECRET=secure-nextauth-secret-admin

# App Configuration
NEXT_PUBLIC_APP_URL=https://admin.yourdomain.com
NODE_ENV=production

# Activity Logging
ENABLE_ACTIVITY_LOGGING=true
LOG_RETENTION_DAYS=365

# Staff Server Integration
STAFF_SERVER_API_KEY=secure-shared-api-key
STAFF_SERVER_URL=https://staff.yourdomain.com
AUTO_CREATE_INVOICE=true
DEFAULT_MONTHLY_FEE=100

# Payment Configuration (Optional)
STRIPE_SECRET_KEY=sk_live_...
STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...
```

#### Build and Deploy
```bash
cd admin-server
npm install
npm run build
npm start
```

### 2. Staff Server Deployment

#### Environment Configuration
Create `.env.local` in `staff-server/`:
```env
# Database
DATABASE_URL=postgresql://staff_owner:password@host:port/staff?sslmode=require

# Authentication
JWT_SECRET=secure-staff-jwt-secret-production
JWT_EXPIRES_IN=24h

# App Configuration
NEXT_PUBLIC_APP_URL=https://staff.yourdomain.com
NODE_ENV=production

# Activity Logging
ENABLE_ACTIVITY_LOGGING=true
LOG_RETENTION_DAYS=365

# Admin Server Integration
ADMIN_SERVER_URL=https://admin.yourdomain.com
ADMIN_SERVER_API_KEY=secure-shared-api-key
```

#### Build and Deploy
```bash
cd staff-server
npm install
npm run build
npm start
```

### 3. Database Migration

#### Admin Server Database
```bash
cd admin-server
npm run db:migrate
node scripts/add-users.js
```

#### Staff Server Database
```bash
cd staff-server
npm run db:migrate
node scripts/add-users.js
```

### 4. Integration Testing

Run the integration test suite:
```bash
# Set environment variables
export ADMIN_SERVER_URL=https://admin.yourdomain.com
export STAFF_SERVER_URL=https://staff.yourdomain.com
export STAFF_SERVER_API_KEY=secure-shared-api-key

# Run integration tests
node scripts/test-integration.js
```

## 🔧 Production Configuration

### SSL/HTTPS Setup
Both servers should be deployed behind HTTPS:
- Use Let's Encrypt for free SSL certificates
- Configure reverse proxy (Nginx/Apache)
- Ensure all API communication uses HTTPS

### Database Security
- Use connection pooling
- Enable SSL connections
- Regular backups
- Monitor connection limits

### API Security
- Secure API keys (use environment variables)
- Rate limiting
- CORS configuration
- Input validation

## 🔗 Integration Configuration

### API Key Setup
Both servers must use the same API key for secure communication:

1. Generate a secure API key:
```bash
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
```

2. Set in both servers:
   - Admin Server: `STAFF_SERVER_API_KEY`
   - Staff Server: `ADMIN_SERVER_API_KEY`

### Cross-Server Communication
The integration enables:
- Student enrollment notifications
- Financial data access
- Activity log synchronization
- Consolidated reporting

### Health Checks
Monitor integration status:
- Admin Server: `GET /api/health`
- Staff Server: `GET /api/health`
- Integration: `GET /api/admin-integration/status` (Staff Server)

## 📊 Monitoring & Logging

### Application Monitoring
- Monitor server uptime
- Track API response times
- Monitor database connections
- Track integration status

### Activity Logging
Both servers maintain comprehensive audit trails:
- User authentication events
- Data modifications
- Cross-server communications
- Error tracking

### Log Retention
Configure log retention policies:
- Activity logs: 365 days (configurable)
- Error logs: 90 days
- Access logs: 30 days

## 🔄 Backup Strategy

### Database Backups
- Daily automated backups
- Point-in-time recovery
- Cross-region backup storage
- Regular restore testing

### Application Backups
- Source code in version control
- Environment configuration backup
- SSL certificate backup
- Documentation backup

## 🚨 Disaster Recovery

### Recovery Procedures
1. **Database Recovery**: Restore from latest backup
2. **Application Recovery**: Redeploy from version control
3. **Integration Recovery**: Verify API keys and connectivity
4. **Data Validation**: Run integration tests

### Failover Strategy
- Database failover to secondary instance
- Application deployment to backup servers
- DNS failover for minimal downtime
- Integration re-establishment

## 🔧 Maintenance

### Regular Updates
- Security patches
- Dependency updates
- Database maintenance
- SSL certificate renewal

### Performance Optimization
- Database query optimization
- Connection pool tuning
- Cache configuration
- CDN setup for static assets

## 📈 Scaling Considerations

### Horizontal Scaling
- Load balancer configuration
- Session management
- Database read replicas
- Microservice architecture

### Vertical Scaling
- Server resource monitoring
- Database performance tuning
- Memory optimization
- CPU utilization tracking

## 🛠️ Troubleshooting

### Common Issues

#### Integration Connectivity
```bash
# Test admin server from staff server
curl -H "X-API-Key: your-api-key" \
     -H "X-Source-Service: staff-server" \
     https://admin.yourdomain.com/api/health
```

#### Database Connection
```bash
# Test database connectivity
node scripts/test-db.js
```

#### Authentication Issues
- Verify JWT secrets are set
- Check token expiration settings
- Validate user credentials

### Log Analysis
- Check application logs for errors
- Monitor database query performance
- Review integration communication logs
- Analyze user activity patterns

## 📞 Support

### Health Check Endpoints
- Admin Server: `https://admin.yourdomain.com/api/health`
- Staff Server: `https://staff.yourdomain.com/api/health`
- Integration Status: `https://staff.yourdomain.com/api/admin-integration/status`

### Monitoring Dashboard
Access the integration status dashboard:
- Staff Server: `https://staff.yourdomain.com/dashboard/integration`

### Emergency Contacts
- System Administrator
- Database Administrator
- Development Team
- Infrastructure Team

## 📝 Post-Deployment Checklist

- [ ] Both servers are running and accessible
- [ ] Database migrations completed successfully
- [ ] Integration tests pass
- [ ] SSL certificates are valid
- [ ] Monitoring is configured
- [ ] Backups are working
- [ ] User accounts are created
- [ ] Documentation is updated
- [ ] Team is trained on new system

## 🔐 Security Checklist

- [ ] All API keys are secure and unique
- [ ] HTTPS is enforced
- [ ] Database connections are encrypted
- [ ] Input validation is implemented
- [ ] Rate limiting is configured
- [ ] CORS is properly set up
- [ ] Activity logging is enabled
- [ ] Regular security updates are scheduled
