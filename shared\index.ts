/**
 * SHARED LIBRARY MAIN EXPORT
 * Centralizes all shared utilities, services, and types
 */

// Types
export * from './types/common';
export * from './types/database';

// Utilities
export * from './utils/api-response';
export * from './utils/auth';
export * from './utils/validation';

// Services
export * from './services/database';
export * from './services/inter-server';

// Middleware
export * from './middleware/auth';

// Configuration
export * from './config/security';

// Re-export commonly used types for convenience
export type {
  UserRole,
  ServerType,
  ApiResponse,
  ValidationResult,
  PaginationParams
} from './types/common';

export type {
  DatabaseUser,
  DatabaseConfig,
  QueryResult
} from './types/database';

export type {
  AuthContext,
  AuthMiddlewareOptions,
  SecurityConfig
} from './middleware/auth';

export type {
  InterServerResponse,
  UserSyncData,
  StudentData,
  PaymentData
} from './services/inter-server';
