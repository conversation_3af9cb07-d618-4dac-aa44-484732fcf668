/**
 * UNIFIED API RESPONSE UTILITIES
 * Fixes inconsistent error handling across admin and staff servers
 */

import { NextResponse } from 'next/server';
import { ApiResponse, HttpStatus, ErrorCode } from '../types/common';

/**
 * Create a standardized success response
 */
export function createSuccessResponse<T>(
  data: T, 
  message?: string, 
  status: number = HttpStatus.OK
): NextResponse<ApiResponse<T>> {
  const response: ApiResponse<T> = {
    success: true,
    data,
    message,
    timestamp: new Date()
  };

  return NextResponse.json(response, { status });
}

/**
 * Create a standardized error response
 */
export function createErrorResponse(
  error: string, 
  status: number = HttpStatus.BAD_REQUEST,
  errorCode?: ErrorCode
): NextResponse<ApiResponse<null>> {
  const response: ApiResponse<null> = {
    success: false,
    error,
    timestamp: new Date(),
    ...(errorCode && { errorCode })
  };

  return NextResponse.json(response, { status });
}

/**
 * Create a validation error response
 */
export function createValidationErrorResponse(
  errors: string[]
): NextResponse<ApiResponse<null>> {
  return createErrorResponse(
    `Validation failed: ${errors.join(', ')}`,
    HttpStatus.UNPROCESSABLE_ENTITY,
    ErrorCode.VALIDATION_ERROR
  );
}

/**
 * Create an unauthorized response
 */
export function createUnauthorizedResponse(
  message: string = 'Unauthorized access'
): NextResponse<ApiResponse<null>> {
  return createErrorResponse(
    message,
    HttpStatus.UNAUTHORIZED,
    ErrorCode.UNAUTHORIZED
  );
}

/**
 * Create a forbidden response
 */
export function createForbiddenResponse(
  message: string = 'Access forbidden'
): NextResponse<ApiResponse<null>> {
  return createErrorResponse(
    message,
    HttpStatus.FORBIDDEN,
    ErrorCode.FORBIDDEN
  );
}

/**
 * Create a not found response
 */
export function createNotFoundResponse(
  resource: string = 'Resource'
): NextResponse<ApiResponse<null>> {
  return createErrorResponse(
    `${resource} not found`,
    HttpStatus.NOT_FOUND,
    ErrorCode.NOT_FOUND
  );
}

/**
 * Create a conflict response (for duplicate entries)
 */
export function createConflictResponse(
  message: string = 'Resource already exists'
): NextResponse<ApiResponse<null>> {
  return createErrorResponse(
    message,
    HttpStatus.CONFLICT,
    ErrorCode.DUPLICATE_ENTRY
  );
}

/**
 * Create an internal server error response
 */
export function createInternalErrorResponse(
  message: string = 'Internal server error',
  includeDetails: boolean = false
): NextResponse<ApiResponse<null>> {
  const errorMessage = includeDetails && process.env.DETAILED_ERRORS === 'true' 
    ? message 
    : 'Internal server error';

  return createErrorResponse(
    errorMessage,
    HttpStatus.INTERNAL_SERVER_ERROR,
    ErrorCode.INTERNAL_ERROR
  );
}

/**
 * Create a paginated response
 */
export function createPaginatedResponse<T>(
  data: T[],
  page: number,
  limit: number,
  total: number,
  message?: string
): NextResponse<ApiResponse<{
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}>> {
  const totalPages = Math.ceil(total / limit);
  
  const response: ApiResponse<{
    data: T[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  }> = {
    success: true,
    data: {
      data,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    },
    message,
    timestamp: new Date()
  };

  return NextResponse.json(response);
}

/**
 * Handle async route errors consistently
 */
export function withErrorHandling<T extends any[]>(
  handler: (...args: T) => Promise<NextResponse>
) {
  return async (...args: T): Promise<NextResponse> => {
    try {
      return await handler(...args);
    } catch (error) {
      console.error('API Error:', error);
      
      // Return appropriate error response based on error type
      if (error instanceof Error) {
        return createInternalErrorResponse(error.message, true);
      }
      
      return createInternalErrorResponse();
    }
  };
}

/**
 * Legacy response function for backward compatibility
 * TODO: Remove this after migrating all endpoints
 */
export function createResponse<T>(
  data: T | null,
  success: boolean = true,
  message?: string,
  status: number = HttpStatus.OK
): NextResponse<ApiResponse<T | null>> {
  if (success) {
    return createSuccessResponse(data, message, status);
  } else {
    return createErrorResponse(message || 'Operation failed', status);
  }
}
