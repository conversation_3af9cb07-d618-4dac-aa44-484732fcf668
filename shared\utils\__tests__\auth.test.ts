/**
 * AUTHENTICATION UTILITIES TESTS
 * Tests for shared authentication functions
 */

import {
  hashPassword,
  verifyPassword,
  generateTokens,
  validateTokenAndGetUser,
  hasRole,
  isAdminUser,
  isStaffUser,
} from '../auth';
import { UserRole } from '../../types/common';

// Mock jsonwebtoken
jest.mock('jsonwebtoken', () => ({
  sign: jest.fn(),
  verify: jest.fn(),
}));

// Mock bcryptjs
jest.mock('bcryptjs', () => ({
  hash: jest.fn(),
  compare: jest.fn(),
}));

const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

describe('Authentication Utilities', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('hashPassword', () => {
    it('should hash password with correct rounds', async () => {
      const password = 'testpassword123';
      const hashedPassword = 'hashed_password';
      
      bcrypt.hash.mockResolvedValue(hashedPassword);
      
      const result = await hashPassword(password);
      
      expect(bcrypt.hash).toHaveBeenCalledWith(password, 12);
      expect(result).toBe(hashedPassword);
    });

    it('should use custom rounds when provided', async () => {
      const password = 'testpassword123';
      const rounds = 8;
      
      await hashPassword(password, rounds);
      
      expect(bcrypt.hash).toHaveBeenCalledWith(password, rounds);
    });
  });

  describe('verifyPassword', () => {
    it('should verify password correctly', async () => {
      const password = 'testpassword123';
      const hash = 'hashed_password';
      
      bcrypt.compare.mockResolvedValue(true);
      
      const result = await verifyPassword(password, hash);
      
      expect(bcrypt.compare).toHaveBeenCalledWith(password, hash);
      expect(result).toBe(true);
    });

    it('should return false for incorrect password', async () => {
      const password = 'wrongpassword';
      const hash = 'hashed_password';
      
      bcrypt.compare.mockResolvedValue(false);
      
      const result = await verifyPassword(password, hash);
      
      expect(result).toBe(false);
    });
  });

  describe('generateTokens', () => {
    it('should generate access and refresh tokens', () => {
      const user = {
        id: 'user-123',
        email: '<EMAIL>',
        role: UserRole.ADMIN,
      };
      
      const accessToken = 'access_token';
      const refreshToken = 'refresh_token';
      
      jwt.sign
        .mockReturnValueOnce(accessToken)
        .mockReturnValueOnce(refreshToken);
      
      const result = generateTokens(user);
      
      expect(jwt.sign).toHaveBeenCalledTimes(2);
      expect(result).toEqual({
        token: accessToken,
        refreshToken: refreshToken,
        expiresIn: '24h',
      });
    });
  });

  describe('validateTokenAndGetUser', () => {
    it('should validate token and return user data', () => {
      const token = 'valid_token';
      const userData = {
        id: 'user-123',
        email: '<EMAIL>',
        role: UserRole.ADMIN,
      };
      
      jwt.verify.mockReturnValue(userData);
      
      const result = validateTokenAndGetUser(token);
      
      expect(jwt.verify).toHaveBeenCalledWith(token, process.env.JWT_SECRET);
      expect(result).toEqual({
        success: true,
        user: userData,
      });
    });

    it('should return error for invalid token', () => {
      const token = 'invalid_token';
      
      jwt.verify.mockImplementation(() => {
        throw new Error('Invalid token');
      });
      
      const result = validateTokenAndGetUser(token);
      
      expect(result).toEqual({
        success: false,
        error: 'Invalid token',
      });
    });
  });

  describe('hasRole', () => {
    it('should return true when user has required role', () => {
      const userRole = UserRole.ADMIN;
      const requiredRoles = [UserRole.ADMIN, UserRole.CASHIER];
      
      const result = hasRole(userRole, requiredRoles);
      
      expect(result).toBe(true);
    });

    it('should return false when user does not have required role', () => {
      const userRole = UserRole.TEACHER;
      const requiredRoles = [UserRole.ADMIN, UserRole.CASHIER];
      
      const result = hasRole(userRole, requiredRoles);
      
      expect(result).toBe(false);
    });

    it('should return true for empty required roles array', () => {
      const userRole = UserRole.TEACHER;
      const requiredRoles: UserRole[] = [];
      
      const result = hasRole(userRole, requiredRoles);
      
      expect(result).toBe(true);
    });
  });

  describe('isAdminUser', () => {
    it('should return true for admin roles', () => {
      expect(isAdminUser(UserRole.ADMIN)).toBe(true);
      expect(isAdminUser(UserRole.CASHIER)).toBe(true);
      expect(isAdminUser(UserRole.ACCOUNTANT)).toBe(true);
    });

    it('should return false for staff roles', () => {
      expect(isAdminUser(UserRole.MANAGEMENT)).toBe(false);
      expect(isAdminUser(UserRole.RECEPTION)).toBe(false);
      expect(isAdminUser(UserRole.TEACHER)).toBe(false);
    });
  });

  describe('isStaffUser', () => {
    it('should return true for staff roles', () => {
      expect(isStaffUser(UserRole.MANAGEMENT)).toBe(true);
      expect(isStaffUser(UserRole.RECEPTION)).toBe(true);
      expect(isStaffUser(UserRole.TEACHER)).toBe(true);
    });

    it('should return false for admin roles', () => {
      expect(isStaffUser(UserRole.ADMIN)).toBe(false);
      expect(isStaffUser(UserRole.CASHIER)).toBe(false);
      expect(isStaffUser(UserRole.ACCOUNTANT)).toBe(false);
    });
  });
});
