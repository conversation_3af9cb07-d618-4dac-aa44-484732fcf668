/**
 * STAFF SERVER AUTHENTICATION - UPDATED TO USE UNIFIED UTILITIES
 * This fixes authentication dependency issues and uses unified database
 */

import { NextRequest } from 'next/server';
import {
  validateTokenAndGetUser,
  extractToken<PERSON>romHeader,
  hasRole,
  isStaffUser,
  TokenPayload,
  AuthResult
} from '../../../shared/utils/auth';
import { UserRole } from '../../../shared/types/common';

// JWT payload interface
export interface JwtPayload {
  userId: string;
  email: string;
  role: UserRole;
  iat: number;
  exp: number;
}

// Authentication result interface
export interface AuthResult {
  success: boolean;
  user?: {
    id: string;
    email: string;
    role: UserRole;
    name: string;
  };
  error?: string;
}

// Token generation result
export interface TokenResult {
  token: string;
  refreshToken: string;
  expiresIn: number;
}

/**
 * Hash a password using bcrypt
 */
export async function hashPassword(password: string): Promise<string> {
  try {
    const salt = await bcrypt.genSalt(BCRYPT_ROUNDS);
    return await bcrypt.hash(password, salt);
  } catch (error) {
    console.error('Error hashing password:', error);
    throw new Error('Failed to hash password');
  }
}

/**
 * Verify a password against its hash
 */
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  try {
    return await bcrypt.compare(password, hash);
  } catch (error) {
    console.error('Error verifying password:', error);
    return false;
  }
}

/**
 * Generate JWT token and refresh token
 */
export function generateTokens(user: {
  id: string;
  email: string;
  role: UserRole;
}): TokenResult {
  try {
    const payload = {
      userId: user.id,
      email: user.email,
      role: user.role
    };

    const token = jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN } as jwt.SignOptions);
    const refreshToken = jwt.sign(payload, JWT_SECRET, { expiresIn: REFRESH_TOKEN_EXPIRES_IN } as jwt.SignOptions);

    // Calculate expiration time in seconds
    const decoded = jwt.decode(token) as any;
    const expiresIn = decoded.exp - decoded.iat;

    return {
      token,
      refreshToken,
      expiresIn
    };
  } catch (error) {
    console.error('Error generating tokens:', error);
    throw new Error('Failed to generate tokens');
  }
}

/**
 * Verify and decode JWT token
 */
export function verifyToken(token: string): JwtPayload | null {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as JwtPayload;
    return decoded;
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      console.log('Token expired');
    } else if (error instanceof jwt.JsonWebTokenError) {
      console.log('Invalid token');
    } else {
      console.error('Token verification error:', error);
    }
    return null;
  }
}

/**
 * Refresh JWT token using refresh token
 */
export function refreshToken(refreshToken: string): TokenResult | null {
  try {
    const decoded = jwt.verify(refreshToken, JWT_SECRET) as JwtPayload;
    
    // Generate new tokens
    return generateTokens({
      id: decoded.userId,
      email: decoded.email,
      role: decoded.role
    });
  } catch (error) {
    console.error('Error refreshing token:', error);
    return null;
  }
}

/**
 * Extract token from Authorization header
 */
export function extractTokenFromHeader(authHeader: string | null): string | null {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  
  return authHeader.substring(7); // Remove 'Bearer ' prefix
}

/**
 * Get user from request (for API routes)
 */
export async function getUserFromRequest(request: NextRequest): Promise<AuthResult> {
  try {
    const authHeader = request.headers.get('authorization');
    const token = extractTokenFromHeader(authHeader);

    if (!token) {
      return {
        success: false,
        error: 'No token provided'
      };
    }

    // For staff server, validate token with admin server
    try {
      const adminServerUrl = process.env.ADMIN_SERVER_URL || 'http://localhost:3000';
      const response = await fetch(`${adminServerUrl}/api/auth/validate-token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        return {
          success: false,
          error: 'Invalid or expired token'
        };
      }

      const result = await response.json();

      if (!result.success || !result.data.user) {
        return {
          success: false,
          error: 'Invalid token response'
        };
      }

      const user = result.data.user;

      // Ensure user is a staff user (not admin server user)
      if (!['management', 'reception', 'teacher'].includes(user.role)) {
        return {
          success: false,
          error: 'Access denied - staff access required'
        };
      }

      return {
        success: true,
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          name: user.name || user.email.split('@')[0] // Fallback to email prefix
        }
      };
    } catch (fetchError) {
      console.error('Error validating token with admin server:', fetchError);

      // Fallback to local token verification if admin server is unavailable
      const payload = verifyToken(token);

      if (!payload) {
        return {
          success: false,
          error: 'Invalid or expired token'
        };
      }

      return {
        success: true,
        user: {
          id: payload.userId,
          email: payload.email,
          role: payload.role,
          name: payload.email.split('@')[0] // Fallback to email prefix
        }
      };
    }
  } catch (error) {
    console.error('Error getting user from request:', error);
    return {
      success: false,
      error: 'Authentication failed'
    };
  }
}

/**
 * Check if user has required role
 */
export function hasRole(userRole: UserRole, requiredRoles: UserRole[]): boolean {
  return requiredRoles.includes(userRole);
}

/**
 * Check if user has permission for specific action
 */
export function hasPermission(
  userRole: UserRole,
  resource: string,
  action: string
): boolean {
  // Management has access to most things
  if (userRole === UserRole.MANAGEMENT) {
    return true;
  }
  
  // Define role-based permissions for staff server
  const permissions: Record<UserRole, Record<string, string[]>> = {
    [UserRole.MANAGEMENT]: {}, // Management has all permissions
    [UserRole.RECEPTION]: {
      students: ['create', 'read', 'update'],
      leads: ['create', 'read', 'update'],
      groups: ['read'],
      reports: ['read']
    },
    [UserRole.TEACHER]: {
      students: ['read', 'update'],
      groups: ['read', 'update'],
      attendance: ['create', 'read', 'update'],
      reports: ['read']
    },
    // Admin server roles (not applicable here)
    [UserRole.ADMIN]: {},
    [UserRole.CASHIER]: {},
    [UserRole.ACCOUNTANT]: {}
  };
  
  const userPermissions = permissions[userRole];
  const resourcePermissions = userPermissions[resource];
  
  return resourcePermissions?.includes(action) || false;
}

/**
 * Validate password strength
 */
export function validatePasswordStrength(password: string): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  
  if (!/(?=.*[a-z])/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/(?=.*[A-Z])/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/(?=.*\d)/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Create authentication middleware for API routes
 */
export function createAuthMiddleware(requiredRoles?: UserRole[]) {
  return async (request: NextRequest): Promise<AuthResult> => {
    const authResult = await getUserFromRequest(request);
    
    if (!authResult.success || !authResult.user) {
      return authResult;
    }
    
    // Check role requirements
    if (requiredRoles && !hasRole(authResult.user.role, requiredRoles)) {
      return {
        success: false,
        error: 'Insufficient permissions'
      };
    }
    
    return authResult;
  };
}
