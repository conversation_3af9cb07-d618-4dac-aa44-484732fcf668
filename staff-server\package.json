{"name": "innovative-centre-staff", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint", "db:migrate": "node scripts/migrate.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPattern=src", "test:integration": "jest --testPathPattern=tests/integration", "test:ci": "jest --coverage --watchAll=false", "test:related": "jest --findRelatedTests", "test:performance": "jest --testPathPattern=performance", "typecheck": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/pg": "^8.15.4", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "jsonwebtoken": "^9.0.2", "next": "15.3.4", "node-fetch": "^3.3.2", "pg": "^8.16.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.1", "recharts": "^3.0.0", "tailwind-merge": "^3.3.1", "zod": "^3.25.67", "@innovative-platform/shared": "workspace:*"}, "devDependencies": {"@eslint/eslintrc": "^3", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.12", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.4", "eslint-plugin-jest-dom": "^5.1.0", "eslint-plugin-testing-library": "^6.2.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "ts-jest": "^29.1.1", "typescript": "^5"}}