/**
 * Database connection utility for Admin Server
 * Handles PostgreSQL connection to Neon database
 */

import { Pool, PoolClient, QueryResult } from 'pg';

/**
 * FIXED: Standardized database configuration for admin server
 * Now uses unified DATABASE_URL with proper SSL configuration
 */

// Database configuration with fallback support
const dbConfig = {
  // Primary: Use unified DATABASE_URL
  // Fallback: Use legacy ADMIN_DATABASE_URL for backward compatibility
  connectionString: process.env.DATABASE_URL || process.env.ADMIN_DATABASE_URL,

  // FIXED: Proper SSL configuration for production security
  ssl: process.env.NODE_ENV === 'production' ? {
    rejectUnauthorized: process.env.DB_REJECT_UNAUTHORIZED !== 'false'
  } : false,

  // Connection pool settings (configurable via environment)
  max: parseInt(process.env.DB_POOL_MAX || '20'), // Maximum number of clients in the pool
  min: parseInt(process.env.DB_POOL_MIN || '2'), // Minimum number of clients in the pool
  idleTimeoutMillis: parseInt(process.env.DB_POOL_IDLE_TIMEOUT || '30000'), // Close idle clients after 30 seconds
  connectionTimeoutMillis: 10000, // Return an error after 10 seconds if connection could not be established
};

// Create connection pool
let pool: Pool | null = null;

function getPool(): Pool {
  if (!pool) {
    pool = new Pool(dbConfig);
    
    // Handle pool errors
    pool.on('error', (err) => {
      console.error('Unexpected error on idle client', err);
      process.exit(-1);
    });
    
    // Log pool events in development
    if (process.env.NODE_ENV === 'development') {
      pool.on('connect', () => {
        console.log('Database client connected');
      });
      
      pool.on('remove', () => {
        console.log('Database client removed');
      });
    }
  }
  
  return pool;
}

// Database query interface
export interface DbQueryResult<T extends Record<string, any> = any> extends QueryResult<T> {
  rows: T[];
}

// Execute a query with parameters
export async function query<T extends Record<string, any> = any>(
  text: string,
  params?: any[]
): Promise<DbQueryResult<T>> {
  const pool = getPool();
  const start = Date.now();
  
  try {
    const result = await pool.query<T>(text, params);
    const duration = Date.now() - start;
    
    // Log slow queries in development
    if (process.env.NODE_ENV === 'development' && duration > 1000) {
      console.warn(`Slow query detected (${duration}ms):`, text);
    }
    
    return result;
  } catch (error) {
    console.error('Database query error:', error);
    console.error('Query:', text);
    console.error('Params:', params);
    throw error;
  }
}

// Execute a query with a specific client (for transactions)
export async function queryWithClient<T extends Record<string, any> = any>(
  client: PoolClient,
  text: string,
  params?: any[]
): Promise<DbQueryResult<T>> {
  const start = Date.now();
  
  try {
    const result = await client.query<T>(text, params);
    const duration = Date.now() - start;
    
    if (process.env.NODE_ENV === 'development' && duration > 1000) {
      console.warn(`Slow query detected (${duration}ms):`, text);
    }
    
    return result;
  } catch (error) {
    console.error('Database query error:', error);
    console.error('Query:', text);
    console.error('Params:', params);
    throw error;
  }
}

// Get a client from the pool for transactions
export async function getClient(): Promise<PoolClient> {
  const pool = getPool();
  return await pool.connect();
}

// Execute multiple queries in a transaction
export async function transaction<T>(
  callback: (client: PoolClient) => Promise<T>
): Promise<T> {
  const client = await getClient();
  
  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

// Test database connection
export async function testConnection(): Promise<boolean> {
  let testPool: Pool | null = null;

  try {
    console.log('Testing database connection...');

    // Create a test pool with shorter timeouts
    testPool = new Pool({
      connectionString: process.env.DATABASE_URL,
      ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
      max: 1,
      idleTimeoutMillis: 5000,
      connectionTimeoutMillis: 5000,
    });

    const client = await testPool.connect();
    const result = await client.query('SELECT NOW() as current_time');
    client.release();

    console.log('Database connection successful:', result.rows[0].current_time);
    return true;
  } catch (error) {
    console.error('Database connection failed:', error);
    return false;
  } finally {
    if (testPool) {
      try {
        await testPool.end();
      } catch (error) {
        console.error('Error closing test pool:', error);
      }
    }
  }
}

// Close all connections (for graceful shutdown)
export async function closePool(): Promise<void> {
  if (pool) {
    await pool.end();
    pool = null;
    console.log('Database pool closed');
  }
}

// Helper function to build WHERE clauses with parameters
export function buildWhereClause(
  conditions: Record<string, any>,
  startIndex: number = 1
): { whereClause: string; params: any[]; nextIndex: number } {
  const params: any[] = [];
  const clauses: string[] = [];
  let paramIndex = startIndex;
  
  for (const [key, value] of Object.entries(conditions)) {
    if (value !== undefined && value !== null) {
      if (Array.isArray(value)) {
        // Handle IN clauses
        const placeholders = value.map(() => `$${paramIndex++}`).join(', ');
        clauses.push(`${key} IN (${placeholders})`);
        params.push(...value);
      } else if (typeof value === 'string' && value.includes('%')) {
        // Handle LIKE clauses
        clauses.push(`${key} ILIKE $${paramIndex++}`);
        params.push(value);
      } else {
        // Handle equality
        clauses.push(`${key} = $${paramIndex++}`);
        params.push(value);
      }
    }
  }
  
  const whereClause = clauses.length > 0 ? `WHERE ${clauses.join(' AND ')}` : '';
  
  return {
    whereClause,
    params,
    nextIndex: paramIndex
  };
}

// Helper function to build ORDER BY clause
export function buildOrderByClause(
  sortBy?: string,
  sortOrder: 'asc' | 'desc' = 'asc'
): string {
  if (!sortBy) return '';
  
  // Whitelist allowed sort columns to prevent SQL injection
  const allowedColumns = [
    'id', 'email', 'name', 'role', 'created_at', 'updated_at',
    'amount', 'payment_date', 'status', 'payment_type',
    'due_date', 'paid_date', 'timestamp'
  ];
  
  if (!allowedColumns.includes(sortBy)) {
    return 'ORDER BY created_at DESC';
  }
  
  return `ORDER BY ${sortBy} ${sortOrder.toUpperCase()}`;
}

// Helper function to build LIMIT and OFFSET clause
export function buildPaginationClause(
  page: number = 1,
  limit: number = 20
): { limitClause: string; offset: number } {
  const offset = (page - 1) * limit;
  const limitClause = `LIMIT ${limit} OFFSET ${offset}`;
  
  return { limitClause, offset };
}

// Export the pool for direct access if needed
export { getPool };
