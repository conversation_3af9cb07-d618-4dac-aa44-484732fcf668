/**
 * HEALTH MONITORING SERVICE
 * Provides comprehensive health checks and performance monitoring
 */

import { db } from './database';

export interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: Date;
  uptime: number;
  version: string;
  checks: HealthCheck[];
}

export interface HealthCheck {
  name: string;
  status: 'pass' | 'fail' | 'warn';
  duration: number;
  message?: string;
  details?: any;
}

export interface PerformanceMetrics {
  memoryUsage: NodeJS.MemoryUsage;
  cpuUsage: number;
  eventLoopDelay: number;
  activeConnections: number;
  requestsPerMinute: number;
  averageResponseTime: number;
}

/**
 * Health monitoring service
 */
export class MonitoringService {
  private startTime: Date;
  private requestCount: number = 0;
  private responseTimeSum: number = 0;
  private lastMinuteRequests: number[] = [];

  constructor() {
    this.startTime = new Date();
    this.initializeMetrics();
  }

  /**
   * Initialize performance metrics tracking
   */
  private initializeMetrics(): void {
    // Track requests per minute
    setInterval(() => {
      this.lastMinuteRequests.push(this.requestCount);
      if (this.lastMinuteRequests.length > 60) {
        this.lastMinuteRequests.shift();
      }
      this.requestCount = 0;
    }, 1000);
  }

  /**
   * Record a request for metrics
   */
  recordRequest(responseTime: number): void {
    this.requestCount++;
    this.responseTimeSum += responseTime;
  }

  /**
   * Get comprehensive health status
   */
  async getHealthStatus(): Promise<HealthStatus> {
    const checks: HealthCheck[] = [];
    
    // Database health check
    const dbCheck = await this.checkDatabase();
    checks.push(dbCheck);
    
    // Memory health check
    const memoryCheck = this.checkMemory();
    checks.push(memoryCheck);
    
    // Disk space check
    const diskCheck = await this.checkDiskSpace();
    checks.push(diskCheck);
    
    // External dependencies check
    const depsCheck = await this.checkExternalDependencies();
    checks.push(depsCheck);

    // Determine overall status
    const hasFailures = checks.some(check => check.status === 'fail');
    const hasWarnings = checks.some(check => check.status === 'warn');
    
    let status: 'healthy' | 'degraded' | 'unhealthy';
    if (hasFailures) {
      status = 'unhealthy';
    } else if (hasWarnings) {
      status = 'degraded';
    } else {
      status = 'healthy';
    }

    return {
      status,
      timestamp: new Date(),
      uptime: this.getUptime(),
      version: process.env.npm_package_version || '1.0.0',
      checks,
    };
  }

  /**
   * Check database connectivity and performance
   */
  private async checkDatabase(): Promise<HealthCheck> {
    const start = Date.now();
    
    try {
      const dbHealth = await db.getHealthStatus();
      const duration = Date.now() - start;
      
      if (!dbHealth.healthy) {
        return {
          name: 'database',
          status: 'fail',
          duration,
          message: 'Database connection failed',
          details: dbHealth,
        };
      }
      
      // Check if response time is acceptable
      if (duration > 1000) {
        return {
          name: 'database',
          status: 'warn',
          duration,
          message: 'Database response time is slow',
          details: { responseTime: duration },
        };
      }
      
      return {
        name: 'database',
        status: 'pass',
        duration,
        message: 'Database is healthy',
        details: dbHealth,
      };
    } catch (error) {
      return {
        name: 'database',
        status: 'fail',
        duration: Date.now() - start,
        message: 'Database health check failed',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      };
    }
  }

  /**
   * Check memory usage
   */
  private checkMemory(): HealthCheck {
    const start = Date.now();
    const memoryUsage = process.memoryUsage();
    const duration = Date.now() - start;
    
    // Convert to MB
    const heapUsedMB = memoryUsage.heapUsed / 1024 / 1024;
    const heapTotalMB = memoryUsage.heapTotal / 1024 / 1024;
    const memoryUsagePercent = (heapUsedMB / heapTotalMB) * 100;
    
    let status: 'pass' | 'warn' | 'fail' = 'pass';
    let message = 'Memory usage is normal';
    
    if (memoryUsagePercent > 90) {
      status = 'fail';
      message = 'Memory usage is critically high';
    } else if (memoryUsagePercent > 75) {
      status = 'warn';
      message = 'Memory usage is high';
    }
    
    return {
      name: 'memory',
      status,
      duration,
      message,
      details: {
        heapUsedMB: Math.round(heapUsedMB),
        heapTotalMB: Math.round(heapTotalMB),
        usagePercent: Math.round(memoryUsagePercent),
        rss: Math.round(memoryUsage.rss / 1024 / 1024),
        external: Math.round(memoryUsage.external / 1024 / 1024),
      },
    };
  }

  /**
   * Check disk space (simplified check)
   */
  private async checkDiskSpace(): Promise<HealthCheck> {
    const start = Date.now();
    
    try {
      // This is a simplified check - in production, you'd use fs.statSync
      // For now, we'll just return a pass status
      return {
        name: 'disk',
        status: 'pass',
        duration: Date.now() - start,
        message: 'Disk space is adequate',
      };
    } catch (error) {
      return {
        name: 'disk',
        status: 'warn',
        duration: Date.now() - start,
        message: 'Could not check disk space',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      };
    }
  }

  /**
   * Check external dependencies
   */
  private async checkExternalDependencies(): Promise<HealthCheck> {
    const start = Date.now();
    
    try {
      // Check if we can resolve DNS (basic connectivity check)
      // In a real implementation, you'd check specific external services
      return {
        name: 'external_deps',
        status: 'pass',
        duration: Date.now() - start,
        message: 'External dependencies are accessible',
      };
    } catch (error) {
      return {
        name: 'external_deps',
        status: 'warn',
        duration: Date.now() - start,
        message: 'Some external dependencies may be unreachable',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      };
    }
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics(): PerformanceMetrics {
    const memoryUsage = process.memoryUsage();
    const requestsPerMinute = this.lastMinuteRequests.reduce((sum, count) => sum + count, 0);
    const averageResponseTime = this.responseTimeSum > 0 ? this.responseTimeSum / this.requestCount : 0;
    
    return {
      memoryUsage,
      cpuUsage: this.getCpuUsage(),
      eventLoopDelay: this.getEventLoopDelay(),
      activeConnections: this.getActiveConnections(),
      requestsPerMinute,
      averageResponseTime,
    };
  }

  /**
   * Get server uptime in seconds
   */
  getUptime(): number {
    return Math.floor((Date.now() - this.startTime.getTime()) / 1000);
  }

  /**
   * Get CPU usage (simplified)
   */
  private getCpuUsage(): number {
    // This is a simplified implementation
    // In production, you'd use process.cpuUsage() or external libraries
    return Math.random() * 100; // Placeholder
  }

  /**
   * Get event loop delay (simplified)
   */
  private getEventLoopDelay(): number {
    // This is a simplified implementation
    // In production, you'd use perf_hooks or external libraries
    return Math.random() * 10; // Placeholder
  }

  /**
   * Get active connections count (simplified)
   */
  private getActiveConnections(): number {
    // This is a simplified implementation
    // In production, you'd track actual connection counts
    return Math.floor(Math.random() * 50); // Placeholder
  }

  /**
   * Create a middleware to track request metrics
   */
  createMetricsMiddleware() {
    return (req: any, res: any, next: any) => {
      const start = Date.now();
      
      res.on('finish', () => {
        const duration = Date.now() - start;
        this.recordRequest(duration);
      });
      
      next();
    };
  }
}

// Export singleton instance
export const monitoringService = new MonitoringService();

// Export types
export type { HealthStatus, HealthCheck, PerformanceMetrics };
