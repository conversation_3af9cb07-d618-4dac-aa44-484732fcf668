/**
 * UPDATED: Authentication Login API endpoint
 * Now uses unified database service and standardized responses
 */

import { NextRequest } from 'next/server';
import { createSuccessResponse, createErrorResponse, createValidationErrorResponse } from '@/shared/utils/api-response';
import { verifyPassword, generateTokens } from '@/lib/auth';
import { db } from '@/shared/services/database';
import { logAuthEvent, getRequestContext } from '@/lib/activity-logger';
import { validateEmail, validateRequired, combineValidationResults } from '@/shared/utils/validation';
import { loginRateLimiter } from '@/shared/middleware/auth';
import { UserRole } from '@/shared/types/common';

interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

// Use DatabaseUser type from unified service
import type { DatabaseUser } from '@/shared/services/database';

export async function POST(request: NextRequest) {
  try {
    const body: LoginRequest = await request.json();

    // Validate input using unified validation utilities
    const validationResults = combineValidationResults([
      validateRequired(body.email, 'email'),
      validateRequired(body.password, 'password'),
      validateEmail(body.email, 'email')
    ]);

    if (!validationResults.isValid) {
      return createValidationErrorResponse(validationResults.errors);
    }

    const { email, password } = body;

    // Check rate limiting
    const clientIP = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
    const rateLimitKey = `${email}:${clientIP}`;

    if (loginRateLimiter.isRateLimited(rateLimitKey)) {
      return createErrorResponse('Too many login attempts. Please try again later.', 429);
    }

    // Get request context for activity logging
    const context = getRequestContext(request.headers);

    try {
      // Find user using unified database service
      const user = await db.findUserByEmail(email.toLowerCase());

      if (!user) {
        // Log failed login attempt
        console.log(`Failed login attempt for email: ${email}`);
        return createErrorResponse('Invalid email or password', 401);
      }

      // Check if user is active
      if (!user.is_active) {
        console.log(`Login attempt for inactive user: ${email}`);
        return createErrorResponse('Account is deactivated', 401);
      }

      // Check if account is locked
      const isLocked = await db.isUserLocked(user.id);
      if (isLocked) {
        console.log(`Login attempt for locked user: ${email}`);
        return createErrorResponse('Account is temporarily locked due to multiple failed attempts', 423);
      }

      // Verify password
      const isPasswordValid = await verifyPassword(password, user.password_hash);
      if (!isPasswordValid) {
        // Update failed login attempts
        await db.updateUserLogin(user.id, false);
        console.log(`Invalid password for user: ${email}`);
        return createErrorResponse('Invalid email or password', 401);
      }

      // Generate JWT tokens
      const tokens = generateTokens({
        id: user.id,
        email: user.email,
        role: user.role
      });

      // Update successful login tracking
      await db.updateUserLogin(user.id, true);

      // Reset rate limiting on successful login
      loginRateLimiter.reset(rateLimitKey);

      // Log successful login
      await logAuthEvent('LOGIN', user.id, context);

      // Return success response using unified response utility
      return createSuccessResponse({
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          name: user.name,
          serverType: user.server_type
        },
        token: tokens.token,
        refreshToken: tokens.refreshToken,
        expiresIn: tokens.expiresIn
      }, 'Login successful');

    } catch (dbError) {
      console.error('Database error during login:', dbError);
      
      // For development, we can return a mock response if database is not available
      if (process.env.NODE_ENV === 'development' && email === '<EMAIL>') {
        console.log('Using mock authentication for development');
        
        const mockTokens = generateTokens({
          id: 'mock-admin-id',
          email: email,
          role: UserRole.ADMIN
        });

        return createResponse({
          user: {
            id: 'mock-admin-id',
            email: email,
            role: UserRole.ADMIN,
            name: 'Mock Admin User'
          },
          token: mockTokens.token,
          refreshToken: mockTokens.refreshToken,
          expiresIn: mockTokens.expiresIn
        }, true, 'Login successful (development mode)');
      }
      
      return createErrorResponse('Authentication service temporarily unavailable', 503);
    }

  } catch (error) {
    console.error('Login error:', error);
    return createErrorResponse(
      'Login failed due to server error',
      500
    );
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS(request: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
