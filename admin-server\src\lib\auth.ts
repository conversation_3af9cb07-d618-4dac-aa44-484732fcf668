/**
 * ADMIN SERVER AUTHENTICATION - UPDATED TO USE UNIFIED UTILITIES
 * This fixes JWT secret inconsistencies and standardizes authentication
 */

import { NextRequest } from 'next/server';
import {
  generateToken,
  verifyToken,
  hashPassword,
  verifyPassword,
  validateTokenAndGetUser,
  createAuthTokens,
  extractTokenFromHeader,
  hasRole,
  isAdminUser,
  TokenPayload,
  AuthResult
} from '../../../shared/utils/auth';
import { UserRole } from '../../../shared/types/common';

// Re-export types from shared utilities for backward compatibility
export type { TokenPayload as JwtPayload, AuthResult } from '../../../shared/utils/auth';

// Token generation result (keeping for backward compatibility)
export interface TokenResult {
  token: string;
  refreshToken: string;
  expiresIn: number;
}

// Re-export password utilities from shared auth
export { hashPassword, verifyPassword } from '../../../shared/utils/auth';

/**
 * Generate JWT token and refresh token (UPDATED to use unified utilities)
 */
export function generateTokens(user: {
  id: string;
  email: string;
  role: UserRole;
}): TokenResult {
  try {
    const { token, refreshToken } = createAuthTokens(user);

    // Calculate expiration time in seconds for backward compatibility
    const decoded = verifyToken(token);
    const expiresIn = decoded ? (decoded.exp! - decoded.iat!) : 86400; // 24h fallback

    return {
      token,
      refreshToken,
      expiresIn
    };
  } catch (error) {
    console.error('Error generating tokens:', error);
    throw new Error('Failed to generate tokens');
  }
}

// Re-export token verification from shared utilities
export { verifyToken } from '../../../shared/utils/auth';

/**
 * Refresh JWT token using refresh token
 */
export function refreshToken(refreshToken: string): TokenResult | null {
  try {
    const decoded = jwt.verify(refreshToken, JWT_SECRET) as JwtPayload;
    
    // Generate new tokens
    return generateTokens({
      id: decoded.userId,
      email: decoded.email,
      role: decoded.role
    });
  } catch (error) {
    console.error('Error refreshing token:', error);
    return null;
  }
}

/**
 * Extract token from Authorization header
 */
export function extractTokenFromHeader(authHeader: string | null): string | null {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  
  return authHeader.substring(7); // Remove 'Bearer ' prefix
}

/**
 * Get user from request (for API routes)
 */
export async function getUserFromRequest(request: NextRequest): Promise<AuthResult> {
  try {
    const authHeader = request.headers.get('authorization');
    const token = extractTokenFromHeader(authHeader);
    
    if (!token) {
      return {
        success: false,
        error: 'No token provided'
      };
    }
    
    const payload = verifyToken(token);
    
    if (!payload) {
      return {
        success: false,
        error: 'Invalid or expired token'
      };
    }
    
    return {
      success: true,
      user: {
        id: payload.userId,
        email: payload.email,
        role: payload.role,
        name: '' // Will be populated from database if needed
      }
    };
  } catch (error) {
    console.error('Error getting user from request:', error);
    return {
      success: false,
      error: 'Authentication failed'
    };
  }
}

/**
 * Check if user has required role
 */
export function hasRole(userRole: UserRole, requiredRoles: UserRole[]): boolean {
  return requiredRoles.includes(userRole);
}

/**
 * Check if user has permission for specific action
 */
export function hasPermission(
  userRole: UserRole,
  resource: string,
  action: string
): boolean {
  // Admin has access to everything
  if (userRole === UserRole.ADMIN) {
    return true;
  }
  
  // Define role-based permissions
  const permissions: Record<UserRole, Record<string, string[]>> = {
    [UserRole.ADMIN]: {}, // Admin has all permissions
    [UserRole.CASHIER]: {
      payments: ['create', 'read', 'update'],
      invoices: ['create', 'read', 'update'],
      reports: ['read']
    },
    [UserRole.ACCOUNTANT]: {
      payments: ['read'],
      invoices: ['read'],
      reports: ['read', 'export'],
      'activity-logs': ['read']
    },
    // Staff server roles (for future use)
    [UserRole.MANAGEMENT]: {},
    [UserRole.RECEPTION]: {},
    [UserRole.TEACHER]: {}
  };
  
  const userPermissions = permissions[userRole];
  const resourcePermissions = userPermissions[resource];
  
  return resourcePermissions?.includes(action) || false;
}

/**
 * Generate secure random string for tokens
 */
export function generateSecureToken(length: number = 32): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return result;
}

/**
 * Validate password strength
 */
export function validatePasswordStrength(password: string): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  
  if (!/(?=.*[a-z])/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/(?=.*[A-Z])/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/(?=.*\d)/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  if (!/(?=.*[@$!%*?&])/.test(password)) {
    errors.push('Password must contain at least one special character (@$!%*?&)');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Create authentication middleware for API routes
 */
export function createAuthMiddleware(requiredRoles?: UserRole[]) {
  return async (request: NextRequest): Promise<AuthResult> => {
    const authResult = await getUserFromRequest(request);
    
    if (!authResult.success || !authResult.user) {
      return authResult;
    }
    
    // Check role requirements
    if (requiredRoles && !hasRole(authResult.user.role, requiredRoles)) {
      return {
        success: false,
        error: 'Insufficient permissions'
      };
    }
    
    return authResult;
  };
}
