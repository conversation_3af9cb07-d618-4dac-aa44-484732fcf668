/**
 * STAFF SERVER LOGIN API - UPDATED WITH UNIFIED AUTHENTICATION
 * Now uses unified database and authentication system
 */

import { NextRequest } from 'next/server';
import { createSuccessResponse, createErrorResponse, createValidationErrorResponse } from '@/shared/utils/api-response';
import { verifyPassword, generateTokens } from '@/lib/auth';
import { db } from '@/shared/services/database';
import { validateEmail, validateRequired, combineValidationResults } from '@/shared/utils/validation';
import { loginRateLimiter } from '@/shared/middleware/auth';
import { logAuthEvent, getRequestContext } from '@/lib/activity-logger';
import { UserRole } from '@/shared/types/common';

interface User {
  id: string;
  email: string;
  password_hash: string;
  role: UserRole;
  name: string;
  is_active: boolean;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, password } = body;

    // Validate required fields
    const validation = validateRequiredFields(body, ['email', 'password']);
    if (!validation.isValid) {
      return createErrorResponse(
        `Missing required fields: ${validation.missingFields.join(', ')}`,
        400
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return createErrorResponse('Invalid email format', 400);
    }

    // Get request context for logging
    const context = getRequestContext(request.headers);

    try {
      // Check if admin integration is enabled
      if (!isAdminIntegrationEnabled()) {
        console.error('Admin integration not enabled - cannot authenticate staff users');
        return createErrorResponse('Authentication service not available', 503);
      }

      // Authenticate via admin server
      const authResult = await adminService.authenticateStaffUser({
        email,
        password
      });

      if (!authResult.success) {
        console.log(`Failed staff authentication for email: ${email} - ${authResult.error}`);
        return createErrorResponse('Invalid email or password', 401);
      }

      // Log successful login (use admin server user ID)
      await logAuthEvent('LOGIN', authResult.user!.id, context);

      // Return success response with admin server authentication data
      return createResponse({
        user: authResult.user,
        token: authResult.token,
        refreshToken: authResult.refreshToken,
        expiresIn: authResult.expiresIn
      }, true, 'Login successful');

    } catch (authError) {
      console.error('Authentication error during staff login:', authError);
      return createErrorResponse('Authentication service temporarily unavailable', 503);
    }

  } catch (error) {
    console.error('Staff login error:', error);
    return createErrorResponse('Invalid request format', 400);
  }
}
