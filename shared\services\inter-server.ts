/**
 * INTER-SERVER COMMUNICATION SERVICE
 * Handles secure communication between admin and staff servers
 * Fixes the problematic admin-service dependencies
 */

import { generateApiKey, verifyApi<PERSON>ey } from '../utils/auth';
import { UserRole } from '../types/common';

export interface ServerConfig {
  baseUrl: string;
  apiKey: string;
  timeout: number;
}

export interface InterServerResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: Date;
}

export interface UserSyncData {
  id: string;
  email: string;
  role: UserRole;
  name: string;
  is_active: boolean;
  server_type: 'admin' | 'staff';
  last_login?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface StudentData {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  level: string;
  status: 'active' | 'inactive' | 'graduated';
  enrollment_date: Date;
  created_at: Date;
  updated_at: Date;
}

export interface PaymentData {
  id: string;
  student_id: string;
  amount: number;
  currency: string;
  payment_method: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  description?: string;
  created_at: Date;
  processed_at?: Date;
}

/**
 * Inter-server communication service
 */
export class InterServerService {
  private adminConfig: ServerConfig;
  private staffConfig: ServerConfig;

  constructor() {
    this.adminConfig = {
      baseUrl: process.env.ADMIN_SERVER_URL || 'http://localhost:3000',
      apiKey: process.env.INTER_SERVER_API_KEY || generateApiKey(),
      timeout: parseInt(process.env.INTER_SERVER_TIMEOUT || '10000')
    };

    this.staffConfig = {
      baseUrl: process.env.STAFF_SERVER_URL || 'http://localhost:3001',
      apiKey: process.env.INTER_SERVER_API_KEY || generateApiKey(),
      timeout: parseInt(process.env.INTER_SERVER_TIMEOUT || '10000')
    };
  }

  /**
   * Make authenticated request to another server
   */
  private async makeRequest<T>(
    config: ServerConfig,
    endpoint: string,
    options: RequestInit = {}
  ): Promise<InterServerResponse<T>> {
    try {
      const url = `${config.baseUrl}/api/internal${endpoint}`;
      
      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': config.apiKey,
          'X-Server-Source': process.env.SERVER_TYPE || 'unknown',
          ...options.headers,
        },
        signal: AbortSignal.timeout(config.timeout),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return {
        success: true,
        data,
        timestamp: new Date()
      };
    } catch (error) {
      console.error(`Inter-server request failed: ${endpoint}`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date()
      };
    }
  }

  // =============================================================================
  // ADMIN SERVER OPERATIONS (called from staff server)
  // =============================================================================

  /**
   * Authenticate user with admin server
   */
  async authenticateWithAdmin(email: string, password: string): Promise<InterServerResponse<{
    user: UserSyncData;
    token: string;
    refreshToken: string;
  }>> {
    return this.makeRequest(this.adminConfig, '/auth/verify', {
      method: 'POST',
      body: JSON.stringify({ email, password })
    });
  }

  /**
   * Get user data from admin server
   */
  async getUserFromAdmin(userId: string): Promise<InterServerResponse<UserSyncData>> {
    return this.makeRequest(this.adminConfig, `/users/${userId}`, {
      method: 'GET'
    });
  }

  /**
   * Sync user data to admin server
   */
  async syncUserToAdmin(userData: Partial<UserSyncData>): Promise<InterServerResponse<UserSyncData>> {
    return this.makeRequest(this.adminConfig, '/users/sync', {
      method: 'POST',
      body: JSON.stringify(userData)
    });
  }

  /**
   * Get students data for payments (called from admin server)
   */
  async getStudentsFromStaff(filters?: {
    search?: string;
    level?: string;
    status?: string;
    limit?: number;
    offset?: number;
  }): Promise<InterServerResponse<{
    students: StudentData[];
    total: number;
  }>> {
    const queryParams = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }

    const endpoint = `/students${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return this.makeRequest(this.staffConfig, endpoint, {
      method: 'GET'
    });
  }

  /**
   * Get specific student data from staff server
   */
  async getStudentFromStaff(studentId: string): Promise<InterServerResponse<StudentData>> {
    return this.makeRequest(this.staffConfig, `/students/${studentId}`, {
      method: 'GET'
    });
  }

  // =============================================================================
  // STAFF SERVER OPERATIONS (called from admin server)
  // =============================================================================

  /**
   * Notify staff server of payment
   */
  async notifyPaymentToStaff(paymentData: PaymentData): Promise<InterServerResponse<void>> {
    return this.makeRequest(this.staffConfig, '/payments/notify', {
      method: 'POST',
      body: JSON.stringify(paymentData)
    });
  }

  /**
   * Update student status on staff server
   */
  async updateStudentOnStaff(
    studentId: string, 
    updates: Partial<StudentData>
  ): Promise<InterServerResponse<StudentData>> {
    return this.makeRequest(this.staffConfig, `/students/${studentId}`, {
      method: 'PATCH',
      body: JSON.stringify(updates)
    });
  }

  /**
   * Get staff server health status
   */
  async getStaffServerHealth(): Promise<InterServerResponse<{
    status: 'healthy' | 'unhealthy';
    uptime: number;
    version: string;
  }>> {
    return this.makeRequest(this.staffConfig, '/health', {
      method: 'GET'
    });
  }

  /**
   * Get admin server health status
   */
  async getAdminServerHealth(): Promise<InterServerResponse<{
    status: 'healthy' | 'unhealthy';
    uptime: number;
    version: string;
  }>> {
    return this.makeRequest(this.adminConfig, '/health', {
      method: 'GET'
    });
  }

  // =============================================================================
  // UTILITY METHODS
  // =============================================================================

  /**
   * Verify API key for incoming inter-server requests
   */
  verifyApiKey(apiKey: string): boolean {
    return verifyApiKey(apiKey);
  }

  /**
   * Test connectivity between servers
   */
  async testConnectivity(): Promise<{
    adminServer: boolean;
    staffServer: boolean;
    errors: string[];
  }> {
    const errors: string[] = [];
    
    const adminHealth = await this.getAdminServerHealth();
    const staffHealth = await this.getStaffServerHealth();

    if (!adminHealth.success) {
      errors.push(`Admin server: ${adminHealth.error}`);
    }

    if (!staffHealth.success) {
      errors.push(`Staff server: ${staffHealth.error}`);
    }

    return {
      adminServer: adminHealth.success,
      staffServer: staffHealth.success,
      errors
    };
  }

  /**
   * Get current server configuration
   */
  getConfig(): {
    admin: Omit<ServerConfig, 'apiKey'>;
    staff: Omit<ServerConfig, 'apiKey'>;
  } {
    return {
      admin: {
        baseUrl: this.adminConfig.baseUrl,
        timeout: this.adminConfig.timeout
      },
      staff: {
        baseUrl: this.staffConfig.baseUrl,
        timeout: this.staffConfig.timeout
      }
    };
  }

  /**
   * Update server configuration
   */
  updateConfig(updates: {
    adminUrl?: string;
    staffUrl?: string;
    apiKey?: string;
    timeout?: number;
  }): void {
    if (updates.adminUrl) {
      this.adminConfig.baseUrl = updates.adminUrl;
    }
    if (updates.staffUrl) {
      this.staffConfig.baseUrl = updates.staffUrl;
    }
    if (updates.apiKey) {
      this.adminConfig.apiKey = updates.apiKey;
      this.staffConfig.apiKey = updates.apiKey;
    }
    if (updates.timeout) {
      this.adminConfig.timeout = updates.timeout;
      this.staffConfig.timeout = updates.timeout;
    }
  }
}

// Export singleton instance
export const interServerService = new InterServerService();

// Export types
export type { ServerConfig, InterServerResponse, UserSyncData, StudentData, PaymentData };
