/**
 * ADMIN USERS API - UPDATED WITH UNIFIED AUTHENTICATION
 * Demonstrates proper use of authentication middleware
 */

import { NextRequest } from 'next/server';
import { withAdminServerAuth, AuthContext, hasPermission } from '@/shared/middleware/auth';
import { createSuccessResponse, createErrorResponse, createValidationErrorResponse } from '@/shared/utils/api-response';
import { db } from '@/shared/services/database';
import { UserRole } from '@/shared/types/common';

// Use DatabaseUser type from unified service
import type { DatabaseUser } from '@/shared/services/database';

/**
 * GET /api/users - List all users (admin server only)
 */
export const GET = withAdminServerAuth(async (request: NextRequest, context: AuthContext) => {
  try {
    // Check if user has permission to view users
    if (!hasPermission(context, 'users:view') && context.user.role !== UserRole.ADMIN) {
      return createErrorResponse('Insufficient permissions to view users', 403);
    }

    // Check permissions
    if (!hasPermission(authResult.user.role, 'users', 'read')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { searchParams } = new URL(request.url);
    const pagination = parsePaginationParams(searchParams);
    const filters = parseFilterParams(searchParams);

    try {
      // Build query with filters (only admin server users)
      let whereClause = 'WHERE server_type = $1';
      const queryParams: any[] = ['admin'];
      let paramIndex = 2;

      if (filters.search) {
        whereClause += ` AND (name ILIKE $${paramIndex} OR email ILIKE $${paramIndex})`;
        queryParams.push(`%${filters.search}%`);
        paramIndex++;
      }

      if (filters.role) {
        whereClause += ` AND role = $${paramIndex}`;
        queryParams.push(filters.role);
        paramIndex++;
      }

      if (filters.isActive !== undefined) {
        whereClause += ` AND is_active = $${paramIndex}`;
        queryParams.push(filters.isActive === 'true');
        paramIndex++;
      }

      // Get total count
      const countResult = await query(
        `SELECT COUNT(*) as total FROM users ${whereClause}`,
        queryParams
      );
      const total = parseInt(countResult.rows[0].total);

      // Get users with pagination
      const offset = (pagination.page - 1) * pagination.limit;
      const usersResult = await query<User>(
        `SELECT id, email, role, name, is_active, created_at, updated_at 
         FROM users ${whereClause} 
         ORDER BY created_at DESC 
         LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`,
        [...queryParams, pagination.limit, offset]
      );

      return createResponse({
        users: usersResult.rows,
        pagination: {
          page: pagination.page,
          limit: pagination.limit,
          total,
          totalPages: Math.ceil(total / pagination.limit),
          hasNext: pagination.page < Math.ceil(total / pagination.limit),
          hasPrev: pagination.page > 1
        }
      }, true, 'Users retrieved successfully');

    } catch (dbError) {
      console.error('Database error getting users:', dbError);
      
      // For development, return mock data if database is not available
      if (process.env.NODE_ENV === 'development') {
        const mockUsers = [
          {
            id: 'mock-admin-id',
            email: '<EMAIL>',
            role: UserRole.ADMIN,
            name: 'System Administrator',
            is_active: true,
            created_at: new Date(),
            updated_at: new Date()
          },
          {
            id: 'mock-cashier-id',
            email: '<EMAIL>',
            role: UserRole.CASHIER,
            name: 'Cashier User',
            is_active: true,
            created_at: new Date(),
            updated_at: new Date()
          }
        ];

        return createResponse({
          users: mockUsers,
          pagination: {
            page: 1,
            limit: 20,
            total: mockUsers.length,
            totalPages: 1,
            hasNext: false,
            hasPrev: false
          }
        }, true, 'Users retrieved successfully (development mode)');
      }
      
      return createErrorResponse('User service temporarily unavailable', 503);
    }

  } catch (error) {
    console.error('Get users error:', error);
    return createErrorResponse('Failed to get users', 500);
  }
}

// POST /api/users - Create new user
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions
    if (!hasPermission(authResult.user.role, 'users', 'create')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const body = await request.json();
    const { validateRequiredFields } = await import('@/lib/utils');
    const { hashPassword } = await import('@/lib/auth');
    const { logUserOperation, getRequestContext } = await import('@/lib/activity-logger');

    // Validate required fields
    const validation = validateRequiredFields(body, ['email', 'password', 'role', 'name']);
    if (!validation.isValid) {
      return createErrorResponse(
        `Missing required fields: ${validation.missingFields.join(', ')}`,
        400
      );
    }

    const { email, password, role, name } = body;

    // Validate role
    const validRoles = ['admin', 'cashier', 'accountant'];
    if (!validRoles.includes(role)) {
      return createErrorResponse('Invalid role. Must be admin, cashier, or accountant', 400);
    }

    const context = getRequestContext(request.headers);

    try {
      // Check if user already exists
      const existingUserResult = await query(
        'SELECT id FROM users WHERE email = $1',
        [email.toLowerCase()]
      );

      if (existingUserResult.rows.length > 0) {
        return createErrorResponse('User with this email already exists', 409);
      }

      // Hash password
      const passwordHash = await hashPassword(password);

      // Create user (admin server users have server_type = 'admin')
      const userResult = await query<User>(
        `INSERT INTO users (email, password_hash, role, name, is_active, server_type)
         VALUES ($1, $2, $3, $4, true, 'admin')
         RETURNING id, email, role, name, is_active, created_at, updated_at`,
        [email.toLowerCase(), passwordHash, role, name]
      );

      const newUser = userResult.rows[0];

      // Log user creation
      await logUserOperation(
        'CREATE' as any,
        authResult.user.id,
        newUser,
        undefined,
        context
      );

      // Return user without password
      return createResponse({
        id: newUser.id,
        email: newUser.email,
        role: newUser.role,
        name: newUser.name,
        isActive: newUser.is_active,
        createdAt: newUser.created_at,
        updatedAt: newUser.updated_at
      }, true, 'User created successfully', undefined, 201);

    } catch (dbError) {
      console.error('Database error creating user:', dbError);

      // For development, return mock response if database is not available
      if (process.env.NODE_ENV === 'development') {
        const mockUser = {
          id: `mock-user-${Date.now()}`,
          email: email,
          role: role,
          name: name,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        return createResponse(mockUser, true, 'User created successfully (development mode)', undefined, 201);
      }

      return createErrorResponse('User creation service temporarily unavailable', 503);
    }

  } catch (error) {
    console.error('Create user error:', error);
    return createErrorResponse('Failed to create user', 500);
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS(request: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
