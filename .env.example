# =============================================================================
# INNOVATIVE CENTRE PLATFORM - UNIFIED ENVIRONMENT CONFIGURATION
# =============================================================================
# CRITICAL: This file has been standardized to fix environment variable issues
# Both servers now use consistent variable names and the same database
# =============================================================================

# =============================================================================
# DATABASE CONFIGURATION (UNIFIED)
# =============================================================================
# IMPORTANT: Both servers now use the SAME database to eliminate sync issues
# This is the primary fix for user management and data consistency problems

DATABASE_URL=postgresql://admin_owner:<EMAIL>/admin?sslmode=require

# Legacy variables (DEPRECATED - will be removed in Phase 2)
ADMIN_DATABASE_URL=postgresql://admin_owner:<EMAIL>/admin?sslmode=require
STAFF_DATABASE_URL=postgresql://admin_owner:<EMAIL>/admin?sslmode=require

# =============================================================================
# SERVER CONFIGURATION (STANDARDIZED PORTS)
# =============================================================================
# Fixed port configuration mismatch between dev and production

# Admin Server
ADMIN_PORT=3000
ADMIN_SERVER_URL=http://localhost:3000
ADMIN_APP_URL=http://localhost:3000

# Staff Server (FIXED: consistent port usage)
STAFF_PORT=3001
STAFF_SERVER_URL=http://localhost:3001
STAFF_APP_URL=http://localhost:3001

# =============================================================================
# AUTHENTICATION & SECURITY (UNIFIED)
# =============================================================================
# Shared JWT secret for token compatibility between servers
JWT_SECRET=your-super-secure-jwt-secret-key-minimum-32-characters-long

# Legacy JWT secrets (for backward compatibility during migration)
ADMIN_JWT_SECRET=your-super-secure-jwt-secret-key-minimum-32-characters-long
STAFF_JWT_SECRET=your-super-secure-jwt-secret-key-minimum-32-characters-long

# NextAuth configuration
NEXTAUTH_SECRET=your-nextauth-secret-here
NEXTAUTH_URL=http://localhost:3000
ADMIN_NEXTAUTH_SECRET=your-nextauth-secret-here
STAFF_NEXTAUTH_SECRET=your-nextauth-secret-here

# Inter-server API key (RENAMED for clarity)
INTER_SERVER_API_KEY=your-secure-api-key-for-server-communication
STAFF_SERVER_API_KEY=your-secure-api-key-for-server-communication

# =============================================================================
# ENVIRONMENT SETTINGS
# =============================================================================
NODE_ENV=development

# =============================================================================
# SECURITY CONFIGURATION (HARDENED)
# =============================================================================
# Password hashing
BCRYPT_ROUNDS=12

# Token expiration
JWT_EXPIRES_IN=24h
REFRESH_TOKEN_EXPIRES_IN=7d

# SSL Configuration (FIXED for production security)
DB_SSL_MODE=require
DB_REJECT_UNAUTHORIZED=true

# =============================================================================
# ACTIVITY LOGGING (UNIFIED)
# =============================================================================
ENABLE_ACTIVITY_LOGGING=true
LOG_RETENTION_DAYS=365
LOG_LEVEL=info
ENABLE_REQUEST_LOGGING=true

# Legacy logging variables (for backward compatibility)
STAFF_ENABLE_ACTIVITY_LOGGING=true
STAFF_LOG_RETENTION_DAYS=365

# =============================================================================
# RATE LIMITING & PERFORMANCE
# =============================================================================
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Database connection pool settings
DB_POOL_MIN=2
DB_POOL_MAX=20
DB_POOL_IDLE_TIMEOUT=30000

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
SMTP_HOST=your-smtp-host
SMTP_PORT=587
SMTP_USER=your-smtp-username
SMTP_PASS=your-smtp-password
FROM_EMAIL=<EMAIL>

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
# Enable detailed error messages in development
DETAILED_ERRORS=true

# Enable CORS for development
ENABLE_CORS=true

# =============================================================================
# PRODUCTION OVERRIDES
# =============================================================================
# Uncomment and set these in production environment:
# NODE_ENV=production
# ADMIN_SERVER_URL=https://admin.innovativecentre.com
# STAFF_SERVER_URL=https://staff.innovativecentre.com
# ADMIN_APP_URL=https://admin.innovativecentre.com
# STAFF_APP_URL=https://staff.innovativecentre.com
# DB_REJECT_UNAUTHORIZED=true
# DETAILED_ERRORS=false
# ENABLE_CORS=false
