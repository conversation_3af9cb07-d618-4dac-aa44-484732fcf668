/**
 * INTERNAL HEALTH CHECK API
 * Provides health status for inter-server communication
 */

import { NextRequest } from 'next/server';
import { createSuccessResponse, createUnauthorizedResponse } from '@/shared/utils/api-response';
import { interServerService } from '@/shared/services/inter-server';
import { db } from '@/shared/services/database';

/**
 * GET /api/internal/health - Health check endpoint
 */
export async function GET(request: NextRequest) {
  try {
    // Verify API key for inter-server communication
    const apiKey = request.headers.get('x-api-key');
    if (!apiKey || !interServerService.verifyApiKey(apiKey)) {
      return createUnauthorizedResponse('Invalid API key');
    }

    // Check database health
    const dbHealth = await db.getHealthStatus();
    
    // Get server uptime
    const uptime = process.uptime();
    
    // Get server version
    const version = process.env.npm_package_version || '1.0.0';

    return createSuccessResponse({
      status: dbHealth.healthy ? 'healthy' : 'unhealthy',
      uptime,
      version,
      database: dbHealth,
      timestamp: new Date(),
      server: 'admin'
    });

  } catch (error) {
    console.error('Health check failed:', error);
    return createSuccessResponse({
      status: 'unhealthy',
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date(),
      server: 'admin'
    });
  }
}
