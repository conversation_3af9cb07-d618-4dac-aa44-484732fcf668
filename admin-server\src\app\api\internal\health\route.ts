/**
 * ENHANCED HEALTH CHECK API
 * Provides comprehensive health status with monitoring
 */

import { NextRequest } from 'next/server';
import { createSuccessResponse, createUnauthorizedResponse, createErrorResponse } from '@/shared/utils/api-response';
import { interServerService } from '@/shared/services/inter-server';
import { monitoringService } from '@/shared/services/monitoring';

/**
 * GET /api/internal/health - Comprehensive health check endpoint
 */
export async function GET(request: NextRequest) {
  try {
    // Check if this is an internal request (requires API key)
    const apiKey = request.headers.get('x-api-key');
    const isInternalRequest = apiKey && interServerService.verifyApiKey(apiKey);

    // For external health checks (load balancers, monitoring), allow without API key
    // but provide limited information
    if (!isInternalRequest && request.headers.get('user-agent')?.includes('HealthCheck')) {
      // Simple health check for load balancers
      const basicHealth = await monitoringService.getHealthStatus();

      if (basicHealth.status === 'unhealthy') {
        return createErrorResponse('Service unhealthy', 503);
      }

      return createSuccessResponse({
        status: basicHealth.status,
        timestamp: basicHealth.timestamp,
        server: 'admin'
      });
    }

    // For internal requests, provide comprehensive health data
    if (!isInternalRequest) {
      return createUnauthorizedResponse('Invalid API key');
    }

    // Get comprehensive health status
    const healthStatus = await monitoringService.getHealthStatus();

    // Get performance metrics
    const performanceMetrics = monitoringService.getPerformanceMetrics();

    const response = {
      ...healthStatus,
      server: 'admin',
      environment: process.env.NODE_ENV || 'development',
      performance: performanceMetrics,
    };

    // Return appropriate status code based on health
    if (healthStatus.status === 'unhealthy') {
      return createErrorResponse(response, 503);
    } else if (healthStatus.status === 'degraded') {
      return createSuccessResponse(response, 'Service degraded', 200);
    } else {
      return createSuccessResponse(response, 'Service healthy', 200);
    }

  } catch (error) {
    console.error('Health check failed:', error);

    const errorResponse = {
      status: 'unhealthy',
      uptime: monitoringService.getUptime(),
      version: process.env.npm_package_version || '1.0.0',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date(),
      server: 'admin'
    };

    return createErrorResponse(errorResponse, 503);
  }
}
