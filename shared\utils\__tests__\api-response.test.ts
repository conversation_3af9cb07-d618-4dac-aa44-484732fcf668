/**
 * API RESPONSE UTILITIES TESTS
 * Tests for standardized API response functions
 */

import {
  createSuccessResponse,
  createErrorResponse,
  createValidationErrorResponse,
  createUnauthorizedResponse,
  createForbiddenResponse,
  createNotFoundResponse,
} from '../api-response';

describe('API Response Utilities', () => {
  describe('createSuccessResponse', () => {
    it('should create success response with data', () => {
      const data = { id: 1, name: 'Test' };
      const message = 'Success';
      
      const response = createSuccessResponse(data, message);
      
      expect(response.status).toBe(200);
      
      // Parse the response body
      const body = JSON.parse(response.body);
      expect(body).toEqual({
        success: true,
        data,
        message,
        timestamp: expect.any(String),
      });
    });

    it('should create success response with custom status code', () => {
      const data = { id: 1 };
      const message = 'Created';
      const statusCode = 201;
      
      const response = createSuccessResponse(data, message, statusCode);
      
      expect(response.status).toBe(201);
    });

    it('should create success response without data', () => {
      const message = 'Operation completed';
      
      const response = createSuccessResponse(null, message);
      
      const body = JSON.parse(response.body);
      expect(body.data).toBeNull();
      expect(body.success).toBe(true);
    });
  });

  describe('createErrorResponse', () => {
    it('should create error response with message', () => {
      const message = 'Something went wrong';
      const statusCode = 500;
      
      const response = createErrorResponse(message, statusCode);
      
      expect(response.status).toBe(500);
      
      const body = JSON.parse(response.body);
      expect(body).toEqual({
        success: false,
        error: message,
        timestamp: expect.any(String),
      });
    });

    it('should use default status code 400', () => {
      const message = 'Bad request';
      
      const response = createErrorResponse(message);
      
      expect(response.status).toBe(400);
    });
  });

  describe('createValidationErrorResponse', () => {
    it('should create validation error response with array of errors', () => {
      const errors = ['Email is required', 'Password is too short'];
      
      const response = createValidationErrorResponse(errors);
      
      expect(response.status).toBe(400);
      
      const body = JSON.parse(response.body);
      expect(body).toEqual({
        success: false,
        error: 'Validation failed',
        validationErrors: errors,
        timestamp: expect.any(String),
      });
    });

    it('should create validation error response with single error', () => {
      const error = 'Email is required';
      
      const response = createValidationErrorResponse(error);
      
      const body = JSON.parse(response.body);
      expect(body.validationErrors).toEqual([error]);
    });
  });

  describe('createUnauthorizedResponse', () => {
    it('should create unauthorized response', () => {
      const message = 'Authentication required';
      
      const response = createUnauthorizedResponse(message);
      
      expect(response.status).toBe(401);
      
      const body = JSON.parse(response.body);
      expect(body.error).toBe(message);
      expect(body.success).toBe(false);
    });

    it('should use default message', () => {
      const response = createUnauthorizedResponse();
      
      const body = JSON.parse(response.body);
      expect(body.error).toBe('Unauthorized');
    });
  });

  describe('createForbiddenResponse', () => {
    it('should create forbidden response', () => {
      const message = 'Access denied';
      
      const response = createForbiddenResponse(message);
      
      expect(response.status).toBe(403);
      
      const body = JSON.parse(response.body);
      expect(body.error).toBe(message);
    });

    it('should use default message', () => {
      const response = createForbiddenResponse();
      
      const body = JSON.parse(response.body);
      expect(body.error).toBe('Forbidden');
    });
  });

  describe('createNotFoundResponse', () => {
    it('should create not found response', () => {
      const message = 'Resource not found';
      
      const response = createNotFoundResponse(message);
      
      expect(response.status).toBe(404);
      
      const body = JSON.parse(response.body);
      expect(body.error).toBe(message);
    });

    it('should use default message', () => {
      const response = createNotFoundResponse();
      
      const body = JSON.parse(response.body);
      expect(body.error).toBe('Not found');
    });
  });

  describe('Response headers', () => {
    it('should include correct content-type header', () => {
      const response = createSuccessResponse({ test: true });
      
      expect(response.headers.get('content-type')).toBe('application/json');
    });

    it('should include CORS headers', () => {
      const response = createSuccessResponse({ test: true });
      
      expect(response.headers.get('access-control-allow-origin')).toBe('*');
      expect(response.headers.get('access-control-allow-methods')).toBe('GET, POST, PUT, DELETE, OPTIONS');
    });
  });
});
