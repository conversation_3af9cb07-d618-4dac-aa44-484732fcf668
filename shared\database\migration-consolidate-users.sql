-- =============================================================================
-- CRITICAL DATABASE MIGRATION: CONSOLIDATE USER MANAGEMENT
-- =============================================================================
-- This migration fixes the dual user table architecture that was causing
-- synchronization issues between admin and staff servers
-- 
-- IMPORTANT: Run this migration during maintenance window
-- =============================================================================

BEGIN;

-- =============================================================================
-- STEP 1: Backup existing data
-- =============================================================================

-- Create backup tables
CREATE TABLE IF NOT EXISTS users_backup_admin AS 
SELECT * FROM users WHERE 1=0;

CREATE TABLE IF NOT EXISTS users_backup_staff AS 
SELECT * FROM users WHERE 1=0;

-- Backup admin users (if any exist)
INSERT INTO users_backup_admin 
SELECT * FROM users 
WHERE server_type = 'admin' OR role IN ('admin', 'cashier', 'accountant');

-- =============================================================================
-- STEP 2: Update admin database users table to be the single source of truth
-- =============================================================================

-- Ensure the users table has all required columns
ALTER TABLE users ADD COLUMN IF NOT EXISTS server_type VARCHAR(20) DEFAULT 'admin';
ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login TIMESTAMP;
ALTER TABLE users ADD COLUMN IF NOT EXISTS failed_login_attempts INTEGER DEFAULT 0;
ALTER TABLE users ADD COLUMN IF NOT EXISTS locked_until TIMESTAMP;

-- Update constraint to include all roles
ALTER TABLE users DROP CONSTRAINT IF EXISTS users_role_check;
ALTER TABLE users ADD CONSTRAINT users_role_check 
CHECK (role IN ('admin', 'cashier', 'accountant', 'management', 'reception', 'teacher'));

-- Update server_type constraint
ALTER TABLE users DROP CONSTRAINT IF EXISTS users_server_type_check;
ALTER TABLE users ADD CONSTRAINT users_server_type_check 
CHECK (server_type IN ('admin', 'staff'));

-- =============================================================================
-- STEP 3: Create indexes for performance
-- =============================================================================

-- Create performance indexes
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_server_type ON users(server_type);
CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active);
CREATE INDEX IF NOT EXISTS idx_users_role_active ON users(role, is_active);

-- =============================================================================
-- STEP 4: Create default admin user if none exists
-- =============================================================================

-- Insert default admin user if no admin exists
INSERT INTO users (
    id,
    email,
    password_hash,
    role,
    name,
    server_type,
    is_active,
    created_at,
    updated_at
)
SELECT 
    uuid_generate_v4(),
    '<EMAIL>',
    '$2a$12$LQv3c1yqBwlVHpPjrCeyL.rHmX9sQ4H9PdgTZNW7aEX1sFAJEFa9O', -- password: admin123
    'admin',
    'System Administrator',
    'admin',
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
WHERE NOT EXISTS (
    SELECT 1 FROM users WHERE role = 'admin' AND is_active = true
);

-- =============================================================================
-- STEP 5: Update activity_logs table to reference unified users table
-- =============================================================================

-- Ensure activity_logs references the correct users table
ALTER TABLE activity_logs DROP CONSTRAINT IF EXISTS activity_logs_user_id_fkey;
ALTER TABLE activity_logs ADD CONSTRAINT activity_logs_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL;

-- =============================================================================
-- STEP 6: Create view for backward compatibility
-- =============================================================================

-- Create view for admin users (for backward compatibility)
CREATE OR REPLACE VIEW admin_users AS
SELECT 
    id,
    email,
    password_hash,
    role,
    name,
    is_active,
    created_at,
    updated_at,
    last_login,
    failed_login_attempts,
    locked_until
FROM users 
WHERE server_type = 'admin' OR role IN ('admin', 'cashier', 'accountant');

-- Create view for staff users (for backward compatibility)
CREATE OR REPLACE VIEW staff_users AS
SELECT 
    id,
    email,
    password_hash,
    role,
    name,
    is_active,
    created_at,
    updated_at,
    last_login,
    failed_login_attempts,
    locked_until
FROM users 
WHERE server_type = 'staff' OR role IN ('management', 'reception', 'teacher');

-- =============================================================================
-- STEP 7: Create functions for user management
-- =============================================================================

-- Function to create a new user with proper server_type assignment
CREATE OR REPLACE FUNCTION create_user(
    p_email VARCHAR(255),
    p_password_hash VARCHAR(255),
    p_role VARCHAR(50),
    p_name VARCHAR(255)
) RETURNS UUID AS $$
DECLARE
    new_user_id UUID;
    server_type VARCHAR(20);
BEGIN
    -- Determine server type based on role
    IF p_role IN ('admin', 'cashier', 'accountant') THEN
        server_type := 'admin';
    ELSE
        server_type := 'staff';
    END IF;
    
    -- Insert new user
    INSERT INTO users (
        id, email, password_hash, role, name, server_type, is_active, created_at, updated_at
    ) VALUES (
        uuid_generate_v4(), p_email, p_password_hash, p_role, p_name, server_type, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    ) RETURNING id INTO new_user_id;
    
    RETURN new_user_id;
END;
$$ LANGUAGE plpgsql;

-- Function to update user login tracking
CREATE OR REPLACE FUNCTION update_user_login(
    p_user_id UUID,
    p_success BOOLEAN DEFAULT true
) RETURNS VOID AS $$
BEGIN
    IF p_success THEN
        UPDATE users 
        SET 
            last_login = CURRENT_TIMESTAMP,
            failed_login_attempts = 0,
            locked_until = NULL
        WHERE id = p_user_id;
    ELSE
        UPDATE users 
        SET 
            failed_login_attempts = COALESCE(failed_login_attempts, 0) + 1,
            locked_until = CASE 
                WHEN COALESCE(failed_login_attempts, 0) + 1 >= 5 
                THEN CURRENT_TIMESTAMP + INTERVAL '30 minutes'
                ELSE locked_until
            END
        WHERE id = p_user_id;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- STEP 8: Grant permissions
-- =============================================================================

-- Grant permissions to application user (adjust username as needed)
GRANT SELECT, INSERT, UPDATE, DELETE ON users TO admin_owner;
GRANT SELECT ON admin_users TO admin_owner;
GRANT SELECT ON staff_users TO admin_owner;
GRANT EXECUTE ON FUNCTION create_user TO admin_owner;
GRANT EXECUTE ON FUNCTION update_user_login TO admin_owner;

-- =============================================================================
-- STEP 9: Verification queries
-- =============================================================================

-- Verify migration success
DO $$
BEGIN
    RAISE NOTICE 'Migration completed successfully!';
    RAISE NOTICE 'Total users: %', (SELECT COUNT(*) FROM users);
    RAISE NOTICE 'Admin users: %', (SELECT COUNT(*) FROM users WHERE server_type = 'admin');
    RAISE NOTICE 'Staff users: %', (SELECT COUNT(*) FROM users WHERE server_type = 'staff');
    RAISE NOTICE 'Active users: %', (SELECT COUNT(*) FROM users WHERE is_active = true);
END $$;

COMMIT;

-- =============================================================================
-- POST-MIGRATION NOTES
-- =============================================================================
-- 1. Update application code to use unified users table
-- 2. Remove staff server users table after confirming migration success
-- 3. Update environment variables to use single DATABASE_URL
-- 4. Test authentication flows for both admin and staff users
-- 5. Monitor application logs for any authentication issues
-- =============================================================================
