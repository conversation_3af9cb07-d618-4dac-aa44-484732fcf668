/**
 * INTERNAL STUDENTS API
 * Provides student data for admin server (payments, reports)
 * Fixes the missing student data synchronization
 */

import { NextRequest } from 'next/server';
import { createSuccessResponse, createErrorResponse, createUnauthorizedResponse } from '@/shared/utils/api-response';
import { interServerService } from '@/shared/services/inter-server';
import { db } from '@/shared/services/database';

/**
 * GET /api/internal/students - Get students for admin server
 */
export async function GET(request: NextRequest) {
  try {
    // Verify API key for inter-server communication
    const apiKey = request.headers.get('x-api-key');
    if (!apiKey || !interServerService.verifyApiKey(apiKey)) {
      return createUnauthorizedResponse('Invalid API key');
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const level = searchParams.get('level');
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Build query
    let query = `
      SELECT 
        id,
        name,
        email,
        phone,
        level,
        status,
        enrollment_date,
        created_at,
        updated_at
      FROM students 
      WHERE 1=1
    `;
    
    const params: any[] = [];
    let paramIndex = 1;

    // Add search filter
    if (search) {
      query += ` AND (name ILIKE $${paramIndex} OR email ILIKE $${paramIndex})`;
      params.push(`%${search}%`);
      paramIndex++;
    }

    // Add level filter
    if (level) {
      query += ` AND level = $${paramIndex}`;
      params.push(level);
      paramIndex++;
    }

    // Add status filter
    if (status) {
      query += ` AND status = $${paramIndex}`;
      params.push(status);
      paramIndex++;
    }

    // Add ordering and pagination
    query += ` ORDER BY name ASC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    params.push(limit, offset);

    // Get students
    const students = await db.query(query, params);

    // Get total count for pagination
    let countQuery = `SELECT COUNT(*) as total FROM students WHERE 1=1`;
    const countParams: any[] = [];
    let countParamIndex = 1;

    if (search) {
      countQuery += ` AND (name ILIKE $${countParamIndex} OR email ILIKE $${countParamIndex})`;
      countParams.push(`%${search}%`);
      countParamIndex++;
    }

    if (level) {
      countQuery += ` AND level = $${countParamIndex}`;
      countParams.push(level);
      countParamIndex++;
    }

    if (status) {
      countQuery += ` AND status = $${countParamIndex}`;
      countParams.push(status);
      countParamIndex++;
    }

    const countResult = await db.query(countQuery, countParams);
    const total = parseInt(countResult[0]?.total || '0');

    return createSuccessResponse({
      students,
      total,
      pagination: {
        limit,
        offset,
        hasMore: offset + limit < total
      }
    });

  } catch (error) {
    console.error('Error fetching students for admin server:', error);
    return createErrorResponse('Failed to fetch students', 500);
  }
}

/**
 * PATCH /api/internal/students/[id] - Update student from admin server
 */
export async function PATCH(request: NextRequest) {
  try {
    // Verify API key for inter-server communication
    const apiKey = request.headers.get('x-api-key');
    if (!apiKey || !interServerService.verifyApiKey(apiKey)) {
      return createUnauthorizedResponse('Invalid API key');
    }

    // Get student ID from URL
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const studentId = pathParts[pathParts.length - 1];

    if (!studentId) {
      return createErrorResponse('Student ID is required', 400);
    }

    // Get update data
    const updates = await request.json();
    
    // Validate allowed fields for inter-server updates
    const allowedFields = ['status', 'level', 'phone', 'email'];
    const filteredUpdates: any = {};
    
    for (const field of allowedFields) {
      if (updates[field] !== undefined) {
        filteredUpdates[field] = updates[field];
      }
    }

    if (Object.keys(filteredUpdates).length === 0) {
      return createErrorResponse('No valid fields to update', 400);
    }

    // Add updated_at timestamp
    filteredUpdates.updated_at = new Date();

    // Build update query
    const setClause = Object.keys(filteredUpdates)
      .map((key, index) => `${key} = $${index + 2}`)
      .join(', ');
    
    const values = [studentId, ...Object.values(filteredUpdates)];
    
    const updateQuery = `
      UPDATE students 
      SET ${setClause}
      WHERE id = $1 
      RETURNING id, name, email, phone, level, status, enrollment_date, created_at, updated_at
    `;

    const result = await db.query(updateQuery, values);

    if (result.length === 0) {
      return createErrorResponse('Student not found', 404);
    }

    return createSuccessResponse(result[0], 'Student updated successfully');

  } catch (error) {
    console.error('Error updating student from admin server:', error);
    return createErrorResponse('Failed to update student', 500);
  }
}
